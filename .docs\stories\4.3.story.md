# Story 4.3: Strategy Control & Monitoring Commands

## Status: Draft

## Story

- As a **System Operator**
- I want **comprehensive strategy control and system monitoring commands through Discord**
- so that **I can monitor system performance, control trading strategies, and respond to emergencies in real-time**

## Acceptance Criteria (ACs)

1. **AC 7.2 (Toggle Command):** The bot **must** provide a `/toggle_strategy [on|off]` command.
2. **AC 7.3 (Security):** Modification commands **must** be restricted to users with a `SystemOperator` Discord role.
3. **AC 4.3.1 (System Status):** Commands **must** provide comprehensive system status monitoring and reporting.
4. **AC 4.3.2 (Trade History):** Commands **must** provide trade history and performance summary access.
5. **AC 4.3.3 (Emergency Control):** Commands **must** provide emergency halt and recovery capabilities.

## Tasks / Subtasks

- [ ] Task 1: Implement `/toggle_strategy` Command (AC: 7.2, 7.3)
  - [ ] Create slash command with on/off parameter options
  - [ ] Implement strategy enable/disable via Redis global status
  - [ ] Add confirmation dialog for strategy state changes
  - [ ] Integrate with GlobalRiskManager halt mechanisms
  - [ ] Add strategy state validation and error handling

- [ ] Task 2: Implement System Status Monitoring Commands (AC: 4.3.1)
  - [ ] Create `/system_status` command for overall system health
  - [ ] Implement `/agent_status` command for individual agent monitoring
  - [ ] Add Redis connection and key status reporting
  - [ ] Create database connection health monitoring
  - [ ] Implement real-time system metrics display

- [ ] Task 3: Implement Trade History Commands (AC: 4.3.2)
  - [ ] Create `/trade_history` command with filtering options
  - [ ] Implement `/performance_summary` command for P&L reporting
  - [ ] Add trade statistics and metrics calculation
  - [ ] Create formatted trade data display for Discord
  - [ ] Implement pagination for large result sets

- [ ] Task 4: Implement Emergency Control Commands (AC: 4.3.3)
  - [ ] Create `/emergency_halt` command for immediate trading stop
  - [ ] Implement `/emergency_resume` command for system recovery
  - [ ] Add `/force_close_positions` command for position management
  - [ ] Create emergency status reporting and confirmation
  - [ ] Implement emergency action audit logging

- [ ] Task 5: Real-time Alert Integration (AC: 4.3.1)
  - [ ] Integrate with MonitoringAgent for system alerts
  - [ ] Create alert subscription and notification system
  - [ ] Implement alert severity levels and filtering
  - [ ] Add alert acknowledgment and management
  - [ ] Create alert history and tracking

- [ ] Task 6: Performance Metrics Integration
  - [ ] Integrate with database for performance calculations
  - [ ] Create real-time P&L monitoring and display
  - [ ] Implement win rate and trade statistics
  - [ ] Add risk metrics and drawdown reporting
  - [ ] Create performance trend analysis and alerts

## Dev Technical Guidance

### Previous Story Insights
Stories 4.1 (Discord Bot Foundation) and 4.2 (Configuration Management) are in Draft status. This story builds upon the bot framework and security infrastructure from previous stories.

### Acceptance Criteria Context [Source: prd/feature-requirements.md#F7]
**AC 7.2 (Toggle Command):** Bot must provide `/toggle_strategy [on|off]` command
**AC 7.3 (Security):** Modification commands must be restricted to `SystemOperator` role

### System Control Integration [Source: architecture/in-memory-data-store-redis.md]
**Critical Redis Keys for Strategy Control:**
- `status:global:trading` - Global halt mechanism (String)
- `permission:BTCUSDT:momentum` - Trading permission control (String with TTL)
- `status:BTCUSDT:trade` - Active trade status monitoring (String)
- `config:MomentumV1:BTCUSDT` - Strategy configuration access (Hash)

### Emergency Halt Mechanisms [Source: prd/feature-requirements.md#F5]
**Global Risk Management Integration:**
- AC 5.3: System halt via `global:status:trading` Redis key with 24-hour TTL
- Emergency halt capability for immediate trading cessation
- Integration with GlobalRiskManager for risk-based halts

### Trade Data Access [Source: architecture/data-models.md]
**Trade Interface for History Commands:**
```typescript
interface Trade {
  id: string; market_id: number; strategy_config_id: number;
  status: 'OPEN' | 'CLOSED' | 'ERROR'; direction: 'LONG' | 'SHORT';
  entry_timestamp: Date; exit_timestamp?: Date | null;
  entry_price: number; exit_price?: number | null;
  quantity: number; leverage: number; pnl?: number | null;
  pnl_percentage?: number | null; entry_signal_id?: string | null;
  exit_signal_id?: string | null; exit_reason?: string | null;
  created_at: Date; updated_at: Date;
}
```

### Database Integration [Source: architecture/database-schema.md]
**Trade History Queries:**
- `trades` table with status, P&L, and timestamp indexing
- `performance_summaries` table for aggregated metrics
- `logs` table for system status and error monitoring
- Efficient querying with proper indexing on market_id and entry_timestamp

### MonitoringAgent Integration [Source: architecture/components.md]
**MonitoringAgent & LoggerWorker:**
- Component Type: Asynchronous Utility Workers
- Responsibility: MonitoringAgent sends human-readable notifications to Discord
- Integration: Real-time alerts and system status notifications

### System Status Monitoring
**Key System Health Indicators:**
- Redis connection status and key availability
- Database connection health and query performance
- Agent status and last activity timestamps
- Trading permission states and global halt status
- Active trade monitoring and position reconciliation

### Discord Command Structure
**Strategy Control Commands:**
```typescript
// /toggle_strategy command
{
  name: 'toggle_strategy',
  description: 'Enable or disable trading strategy',
  options: [
    {
      name: 'action',
      description: 'Strategy action',
      type: ApplicationCommandOptionType.String,
      required: true,
      choices: [
        { name: 'Enable', value: 'on' },
        { name: 'Disable', value: 'off' }
      ]
    }
  ]
}

// /system_status command
{
  name: 'system_status',
  description: 'View overall system health and status'
}

// /trade_history command
{
  name: 'trade_history',
  description: 'View recent trade history',
  options: [
    {
      name: 'limit',
      description: 'Number of trades to show (default: 10)',
      type: ApplicationCommandOptionType.Integer,
      required: false
    },
    {
      name: 'status',
      description: 'Filter by trade status',
      type: ApplicationCommandOptionType.String,
      required: false,
      choices: [
        { name: 'Open', value: 'OPEN' },
        { name: 'Closed', value: 'CLOSED' },
        { name: 'Error', value: 'ERROR' }
      ]
    }
  ]
}
```

### File Locations [Source: architecture/source-tree.md]
- **Strategy Commands:** `apps/trading-app/src/services/discord/commands/strategy.ts`
- **Monitoring Commands:** `apps/trading-app/src/services/discord/commands/monitoring.ts`
- **Emergency Commands:** `apps/trading-app/src/services/discord/commands/emergency.ts`
- **System Utilities:** `apps/trading-app/src/services/discord/utils/system-status.ts`
- **Tests:** `apps/trading-app/tests/services/discord/strategy-commands.test.ts`

### Security Integration [Source: stories/4.1.story.md]
**Role-Based Access Control:**
- SystemOperator role required for all modification commands
- Emergency commands require additional confirmation
- Security validation middleware from Story 4.1
- Comprehensive audit logging for all control actions

### Technical Constraints [Source: architecture/tech-stack.md]
- **Language:** TypeScript 5.4.x with strict type safety
- **Discord Library:** Discord.js with advanced interaction support
- **Database:** PostgreSQL 15.x via Supabase for trade history
- **Cache:** Redis 7.2.x for system status and control
- **Testing:** Vitest 1.x for unit and integration tests
- **Logging:** Pino 9.x for structured logging

### Testing Requirements
Dev Note: Story Requires the following tests:

- [ ] Vitest Unit Tests: (nextToFile: true), coverage requirement: 80%
- [ ] Vitest Integration Test: location: `apps/trading-app/tests/services/discord/strategy-commands.integration.test.ts`
- [ ] Manual Test Steps: Create test scenarios for strategy control and monitoring

Manual Test Steps:
- Dev will test `/toggle_strategy` command with on/off states
- Test system status commands with various system conditions
- Verify trade history commands with different filters and pagination
- Test emergency commands with proper confirmation flows
- Validate real-time alert integration and notification delivery
- Test security restrictions and unauthorized access prevention

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
