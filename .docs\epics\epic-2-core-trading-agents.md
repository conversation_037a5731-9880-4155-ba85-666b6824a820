# Epic 2: Core Trading Agents

## Epic Goal

Implement the four core trading agents that form the heart of the autonomous trading system: MarketRegimeAgent, OrderFlowImbalanceAgent, ExhaustionDetectionAgent, and ExecutionAgent.

## Epic Description

**Existing System Context:**
- Foundation infrastructure is established (Epic 1 complete)
- Database schema and Redis configuration are operational
- LangGraph framework is ready for agent implementation
- External API integrations are configured

**Enhancement Details:**
- What's being built: The four core agents that handle market analysis, signal generation, and trade execution
- How it integrates: Agents communicate via Redis Pub/Sub and share state through Redis keys
- Success criteria: Complete trading workflow from market analysis to trade execution

## Stories

### Story 2.1: MarketRegimeAgent Implementation
**Goal:** Implement the strategic market analysis agent using Grok LLM
**Scope:**
- Scheduled agent that runs every 15 minutes
- Fetches OHLCV data from Binance API
- Constructs LLM prompts for regime classification
- Sets Redis permission keys with TTL fail-safe
- Handles LLM API failures gracefully

**Acceptance Criteria:**
- AC 1.1-1.6 from F1: Market Regime Analysis

### Story 2.2: OrderFlowImbalanceAgent Implementation  
**Goal:** Implement real-time order flow monitoring and entry signal generation
**Scope:**
- Real-time WebSocket connection to Binance trades stream
- Rolling Taker Volume Delta calculation
- Permission checking via Redis
- Entry signal publishing to Redis Pub/Sub
- Robust WebSocket reconnection handling

**Acceptance Criteria:**
- AC 2.1-2.5 from F2: Order Flow Imbalance Detection

### Story 2.3: ExhaustionDetectionAgent Implementation
**Goal:** Implement adaptive exit signal generation based on momentum exhaustion
**Scope:**
- Monitors both trades and order book streams when trade is active
- Detects "aggressor fatigue" and "passive resistance" patterns
- Publishes exit signals to Redis Pub/Sub
- Handles critical failure scenarios with proper recovery

**Acceptance Criteria:**
- AC 3.1-3.4 from F3: Momentum Exhaustion Detection

### Story 2.4: ExecutionAgent Implementation
**Goal:** Implement trade lifecycle management and order execution
**Scope:**
- Subscribes to entry/exit signals via Redis Pub/Sub
- Performs pre-flight safety checks
- Places market orders and trailing stop-losses via Binance API
- Writes trade records to Supabase database
- Implements startup state reconciliation

**Acceptance Criteria:**
- AC 4.1-4.6 from F4: Trade Execution and Management

## Dependencies

**Prerequisites:**
- Epic 1: Foundation & Core Infrastructure (COMPLETE)

**Blocks:**
- Epic 3: Risk Management & Control Systems
- Epic 4: Human Interface & Control Plane

## Acceptance Criteria

- [ ] MarketRegimeAgent runs on schedule and sets permission keys correctly
- [ ] OrderFlowImbalanceAgent detects imbalances and publishes entry signals
- [ ] ExhaustionDetectionAgent monitors trades and publishes exit signals
- [ ] ExecutionAgent manages complete trade lifecycle from entry to exit
- [ ] All agents handle failures gracefully with proper error logging
- [ ] Agent communication via Redis Pub/Sub is reliable
- [ ] WebSocket connections are resilient with automatic reconnection
- [ ] Trade data is accurately recorded in Supabase database
- [ ] Startup reconciliation correctly handles existing positions

## Risk Mitigation

**Primary Risk:** Agent communication failures causing missed signals or orphaned trades
**Mitigation:** Comprehensive error handling, Redis TTL fail-safes, and startup reconciliation
**Rollback Plan:** Disable individual agents via Redis configuration without system restart

## Definition of Done

- [ ] All four core agents are implemented and tested
- [ ] Complete trading workflow functions end-to-end
- [ ] All acceptance criteria from F1-F4 are met
- [ ] Agent failures are handled gracefully without system crashes
- [ ] WebSocket connections are stable and auto-reconnect
- [ ] Trade execution is accurate and properly logged
- [ ] System can recover from restarts without losing trade state
- [ ] Performance meets sub-second signal processing requirements
