# Introduction

## Problem Statement

**User Persona:** Consider "<PERSON>," a technically-skilled quantitative trader. <PERSON> has developed a promising trading thesis based on identifying momentum through order flow imbalances but consistently fails to capture its full potential due to the operational realities of manual execution.

<PERSON> faces three core, interconnected problems that this system aims to solve:
*   **1. Inconsistent Strategy Application:** Despite having a clear set of rules, <PERSON>'s execution is impacted by **emotional bias**. In a real-time environment, fear can cause premature exits from winning trades, while greed can lead to holding losing trades too long, deviating from the optimal strategy.
*   **2. Sub-optimal Execution Price (Slippage):** The window of opportunity to enter a momentum trade is often less than 5 seconds. By the time <PERSON> observes the imbalance, confirms it, and manually enters the order, the price has often moved, resulting in significant **slippage** that erodes the profitability of the strategy.
*   **3. Inability to Scale & Monitor:** The strategy requires constant, focused monitoring of multiple high-frequency data streams (live trades, order book depth). This is not only mentally exhausting but **operationally unscalable**, preventing <PERSON> from being active 24/7 or applying the strategy to more than one market at a time.

## Solution Overview

The Autonomous Trading System is a containerized, agent-based platform designed to serve as a **disciplined, high-performance proxy for the expert human trader**. It systematically addresses the identified problems by codifying <PERSON>'s strategy and executing it with machine-like precision.

*   **Enforcing Absolute Discipline:** Its agent-based logic is entirely rule-based and deterministic at the point of execution.
    -   *Implementation:* All entry, exit, and risk management rules are codified into specialized TypeScript agents. This completely removes emotional input and ensures the strategy is applied with perfect consistency.
*   **Optimizing for Millisecond-Speed:** The system is architected to react to market events at a speed unachievable by a human.
    -   *Implementation:* A "hot path" architecture using real-time WebSockets from Binance and in-memory processing with Redis allows the system to detect and act on an imbalance signal in milliseconds, minimizing slippage.
*   **Providing Total Observability:** The system is not a "black box." It provides a transparent, real-time view into its decision-making process.
    -   *Implementation:* Structured logs are sent to a persistent Supabase database for deep analysis, while critical, human-readable alerts are pushed to a dedicated Discord channel, building operator trust.
*   **Guaranteeing Operator Control:** The system operates autonomously but is always under the ultimate authority of the human operator.
    -   *Implementation:* A secure Discord bot serves as a command-line interface, allowing the operator to dynamically tune all key strategy parameters (`Threshold Manager`) or activate an emergency "kill switch" to halt all trading instantly.

## Goals and Objectives

| Goal | Objective | Key Success Metric(s) | Justification |
| :--- | :--- | :--- | :--- |
| **1. Achieve Profitable Execution** | To successfully deploy and run the defined momentum strategy. | Achieve a positive P&L with a Sharpe Ratio > 1.0 over a 3-month live period. | A Sharpe Ratio > 1.0 is a standard industry benchmark indicating that the strategy generates a positive risk-adjusted return. |
| **2. Ensure Capital Preservation** | To implement robust, multi-layered risk management. | The `GlobalRiskManager` prevents daily drawdown > 3%. No single trade loss exceeds 1% of total equity. | These hard limits ensure that a bug or a "black swan" market event cannot lead to catastrophic capital loss. |
| **3. Deliver Operational Excellence** | To provide a stable, observable, and controllable platform. | >99.9% uptime. All critical decisions are logged with sub-second precision. A full parameter change via Discord is confirmed in < 30 seconds. | The system's value is predicated on its reliability and the operator's ability to trust and control it. |
| **4. Enable Strategic Validation** | To build a reliable framework for testing and optimizing the strategy. | The backtester's P&L is within a 5% margin of a live forward-test on the same data. | This confirms the backtester is a reliable predictor of real-world performance, making it a trustworthy tool for strategy optimization. |
| **5. Build for Future Readiness** | To create a modular platform that can be easily extended. | A new "Mean Reversion" agent can be added to the system without requiring changes to the core trading engine. | This proves the architecture is decoupled and scalable, reducing the cost of future enhancements. |

## Target Audience & "Jobs to be Done" (JTBD)

The sole user is the **System Operator**. The following "Jobs to be Done" frame the core needs the system must fulfill:

*   **JTBD 1 (Control):** "When I see an unexpected market event, I want to **immediately pause all trading activity**, so I can prevent the system from taking losses in an environment it wasn't designed for."
*   **JTBD 2 (Trust):** "When the system executes a trade, I want to **understand the exact data and reasoning that led to that decision**, so I can build confidence in its logic and identify areas for improvement."
*   **JTBD 3 (Adaptability):** "When I believe market volatility has changed, I want to **adjust the strategy's core parameters without redeploying code**, so I can adapt the system to the new environment quickly."
*   **JTBD 4 (Validation):** "Before I change a live parameter, I want to **test its impact against historical data**, so I can make data-driven decisions about the strategy's evolution."

## Scope: In and Out

### In Scope (for V1.0)

*   **Core Trading Engine:**
    -   `MarketRegimeAgent` for LLM-based strategic analysis.
    -   `OrderFlowImbalanceAgent` for entry signal generation.
    -   `ExhaustionDetectionAgent` for adaptive exit signal generation.
    -   `ExecutionAgent` for placing orders with 10x leverage.
*   **Global Risk Management (`GlobalRiskManager`):**
    -   Per-trade position sizing based on a fixed-fractional model.
    -   Configurable daily max drawdown limit ("kill switch").
    -   Configurable concurrency control (max 1 open position).
*   **Dynamic Control (`ThresholdManager` & Discord Bot):**
    -   Remote viewing and updating of all key strategy parameters via Discord slash commands.
    -   Real-time notifications for trade open/close and system alerts.
*   **Backtesting Framework:**
    -   Ability to run the agent logic against historical trade data.
    -   Configurable models for exchange fees and price slippage.
    -   Generation of a performance report with all key metrics.

### Out of Scope (for V1.0)

*   **The `Master AI Agent`:**
    -   *Justification:* The system must first prove its operational stability and the viability of the manual control plane before introducing a layer of autonomous self-optimization.
*   **A Web-Based GUI:**
    -   *Justification:* The target user is a single operator proficient with Discord, making a web GUI an unnecessary development cost for V1.
*   **Multi-Asset / Multi-Strategy Support:**
    -   *Justification:* V1 is focused on perfecting the execution of a single strategy on a single market (BTCUSDT).
*   **Automated Backtest Data Pipeline:**
    -   *Justification:* For V1, the sourcing and preparation of historical data for the backtester will be a manual, offline process.
