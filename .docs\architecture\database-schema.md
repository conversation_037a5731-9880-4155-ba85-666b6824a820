# Database Schema

This section provides the definitive SQL Data Definition Language (DDL) for creating the required tables in the Supabase PostgreSQL database.

```sql
-- Defines the financial instruments available for trading.
CREATE TABLE markets (
    id SERIAL PRIMARY KEY,
    symbol TEXT NOT NULL UNIQUE,
    base_asset TEXT NOT NULL,
    quote_asset TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Stores versioned configurations and parameters for trading strategies.
CREATE TABLE strategy_configs (
    id SERIAL PRIMARY KEY,
    market_id INTEGER NOT NULL REFERENCES markets(id) ON DELETE CASCADE,
    strategy_name TEXT NOT NULL,
    version INTEGER NOT NULL,
    parameters JSONB NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(market_id, strategy_name, version)
);

-- A comprehensive, immutable log of every decision or observation made by any agent.
CREATE TABLE signals (
    id BIGSERIAL PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL,
    market_id INTEGER NOT NULL REFERENCES markets(id) ON DELETE RESTRICT,
    agent_name TEXT NOT NULL,
    signal_type TEXT NOT NULL,
    signal_data JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_signals_timestamp ON signals(timestamp);
CREATE INDEX idx_signals_market_id_timestamp ON signals(market_id, timestamp);

-- The master record of all executed trades.
CREATE TABLE trades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    market_id INTEGER NOT NULL REFERENCES markets(id) ON DELETE RESTRICT,
    strategy_config_id INTEGER NOT NULL REFERENCES strategy_configs(id) ON DELETE RESTRICT,
    status TEXT NOT NULL CHECK (status IN ('OPEN', 'CLOSED', 'ERROR')),
    direction TEXT NOT NULL CHECK (direction IN ('LONG', 'SHORT')),
    entry_timestamp TIMESTAMPTZ NOT NULL,
    exit_timestamp TIMESTAMPTZ,
    entry_price NUMERIC NOT NULL,
    exit_price NUMERIC,
    quantity NUMERIC NOT NULL,
    leverage NUMERIC NOT NULL DEFAULT 10.0,
    entry_signal_id BIGINT REFERENCES signals(id) ON DELETE SET NULL,
    exit_signal_id BIGINT REFERENCES signals(id) ON DELETE SET NULL,
    exit_reason TEXT,
    pnl NUMERIC,
    pnl_percentage NUMERIC,
    exchange_trade_id TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_trades_status ON trades(status);
CREATE INDEX idx_trades_market_id_entry_timestamp ON trades(market_id, entry_timestamp);

-- Stores structured application logs for debugging and auditing.
CREATE TABLE logs (
    id BIGSERIAL PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    log_level TEXT NOT NULL CHECK (log_level IN ('DEBUG', 'INFO', 'WARN', 'ERROR', 'CRITICAL')),
    message TEXT NOT NULL,
    metadata JSONB
);
CREATE INDEX idx_logs_timestamp ON logs(timestamp);

-- Stores periodic KPI summaries for the Master AI Agent.
CREATE TABLE performance_summaries (
    id BIGSERIAL PRIMARY KEY,
    strategy_config_id INTEGER NOT NULL REFERENCES strategy_configs(id),
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    total_trades INTEGER NOT NULL,
    win_rate NUMERIC,
    pnl_percentage NUMERIC,
    sharpe_ratio NUMERIC,
    max_drawdown NUMERIC,
    parameters_used JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_perf_summary_strategy_period ON performance_summaries(strategy_config_id, period_end);

-- Utility function to automatically update 'updated_at' timestamps.
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_timestamp_trades
BEFORE UPDATE ON trades
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
```
