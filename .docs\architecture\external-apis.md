# External APIs

This section provides detailed specifications for integrating with third-party services.

## Binance (USDⓈ-M Futures) API (via CCXT Pro)
-   **Purpose:** The primary interface to the market for both real-time data and trade execution. The integration uses a dual approach.
-   **Usage Pattern 1: Real-Time Data Streams (WebSocket):**
    -   Used by `OrderFlowImbalanceAgent` and `ExhaustionDetectionAgent` for `watchTrades('BTC/USDT:USDT')` and `watchOrderBook('BTC/USDT:USDT', 50)`.
-   **Usage Pattern 2: Command & Control (REST API):**
    -   Used by `ExecutionAgent` and `MarketRegimeAgent` for `createOrder(...)`, `cancelOrder(...)`, `fetchBalance()`, and `fetchOHLCV(...)`.
-   **Resilience:** Requires robust management of WebSocket reconnections and error handling for all REST API calls.

## LLM API (Grok)
-   **Purpose:** Provides high-level analytical capabilities for strategic agents.
-   **Authentication:** API Key.
-   **Failure Strategy:** All API calls must have an aggressive timeout. On failure or invalid response, the system must default to a "safe" state (e.g., blocking trades).

## Discord API
-   **Purpose:** Serves as the system's primary interface for human monitoring and control.
-   **Integration:** Uses both simple Webhooks for notifications and the full Bot User API for interactive slash commands.
-   **Security:** Requires role-based access control to restrict modification commands to the `SystemOperator` role.

## Supabase Platform
-   **Purpose:** Provides the hosted PostgreSQL database for "cold path" data storage.
-   **Authentication:** Direct database connection string.
-   **Connection Management:** A connection pool must be implemented to manage database connections efficiently.
-   **Security:** The Supabase project must be configured with network restrictions, allowing connections only from the known IP address of the trading VPS.
