# In-Memory Data Store (Redis)

This section defines the structure and conventions for the "hot path" data stored in Redis. A consistent key naming convention is used: `[context]:[asset_symbol]:[type]`.

## Redis Structures

| Role | Redis Data Type | Example Key / Channel | Producer Agent(s) | Consumer Agent(s) | Purpose |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **State: Permission** | String | `permission:BTCUSDT:momentum` | MarketRegimeAgent | OrderFlowImbalanceAgent | Grants/revokes trading permission. Set with a TTL as a fail-safe. |
| **State: Trade Lock** | String | `status:BTCUSDT:trade` | ExecutionAgent | ExecutionAgent, EDA | Prevents concurrent trades and activates the `ExhaustionDetectionAgent`. |
| **State: Global Lock** | String | `status:global:trading` | GlobalRiskManager | ExecutionAgent | Halts all new trading system-wide (e.g., on max drawdown). |
| **Messaging: Signals** | Pub/Sub Channel | `signals:BTCUSDT` | Imbalance, Exhaustion | ExecutionAgent, Monitoring | Real-time, fire-and-forget signals for entry and exit. |
| **Queue: Logging** | List | `queue:logging` | All Agents | LoggerWorker | A high-speed buffer for log jobs to be persisted asynchronously. |
| **Config: Parameters**| Hash | `config:MomentumV1:BTCUSDT` | DiscordBot, Master AI | All Trading Agents | Stores live, tunable strategy parameters (`Threshold Manager`). |
