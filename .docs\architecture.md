Absolutely. Here is the definitive, complete, and fully detailed **V1.0 Architecture Document** for the **Autonomous Trading System**.

This single document contains all ten sections, with every sub-section fully expanded to provide maximum clarity and detail. It is the culmination of our entire design process and is ready to be used as the master blueprint for development.

***

# **Autonomous Trading System Architecture Document**

## **1. Introduction**

This document outlines the overall project architecture for the Autonomous Trading System, including backend systems, shared services, and non-UI specific concerns. Its primary goal is to serve as the guiding architectural blueprint for AI-driven development, ensuring consistency and adherence to chosen patterns and technologies.

This system is designed as a containerized, event-driven application, decomposed into specialized agents orchestrated by LangGraph. A core principle is the separation of the high-frequency **'hot path'** for real-time state and messaging (managed by **Redis**) from the durable **'cold path'** for analytics and permanent record-keeping (managed by a **Supabase PostgreSQL** database). The entire system is designed for operational maturity, packaged with **Docker**, managed via **Docker Compose**, and deployed to a Virtual Private Server (VPS) through an automated CI/CD pipeline. Human oversight and dynamic control are facilitated through a Discord-based command interface, with a clear path to future self-optimization via a `Master AI Agent`.

**Relationship to Frontend Architecture:**
This project's primary interface is a Discord-based control plane. As such, a dedicated Frontend Architecture Document is not required. All UI/UX concerns are limited to the functionality of the Discord bot.

### **Starter Template or Existing Project**

N/A - This is a greenfield project. The architecture is designed from first principles. All tooling, containerization, and configuration will be set up manually as defined within this document.

### **Change Log**

| Date       | Version | Description      | Author   |
| :--------- | :------ | :--------------- | :------- |
| {{CUR_DATE}} | 1.0     | Initial Draft    | Winston  |

---

## **2. High Level Architecture**

### **Technical Summary**

The Autonomous Trading System is an event-driven, agent-based application architected for high performance and operational maturity. It is deployed as a set of containerized services on a dedicated VPS using Docker and Docker Compose. The core design principle is a strict separation of the **hot data path** from the **cold data path**. The hot path handles real-time, low-latency state and inter-agent messaging using **Redis**. The cold data path ensures durable, analytical storage of all trades, signals, and logs in a **Supabase PostgreSQL** database. This architecture is designed for reliable, automated deployment via a standard CI/CD pipeline and includes a meta-learning feedback loop for future self-optimization.

### **C4-Style Container Diagram**

This diagram provides a detailed view of the containers running within the VPS and their interactions.

```mermaid
graph TD
    subgraph "External Systems"
        A[Operator]
        B[Binance USDⓈ-M Futures]
        C[LLM API (Grok)]
        D[Supabase Platform]
        E[Discord API]
    end

    subgraph "Your VPS"
        direction LR
        subgraph "Docker Environment"
            boundary[Container Boundary]

            subgraph "Container: trading-app"
                TA[LangGraph Agents]
                TA_API[Discord Command API]
            end

            subgraph "Container: logger-worker"
                LW[Logger Process]
            end

            subgraph "Container: redis"
                R[(Redis)]
            end
        end
    end

    %% User Interaction Flow
    A -- "/slash commands via HTTPS" --> E
    E -- "Webhook/API Call" --> TA_API

    %% Hot Path Trading Flow
    TA -- "Streams market data & places orders via WebSocket/REST" --> B
    TA -- "Reads/writes state & Pub/Sub messages" --> R

    %% Cold Path & Strategic Flow
    TA -- "Makes API calls for analysis" --> C
    TA -- "Pushes log jobs to queue" --> R
    LW -- "Pops log jobs from queue" --> R
    LW -- "Writes logs to database" --> D
    TA -- "Writes final trade records" --> D
```

### **Architectural and Design Patterns**

-   **Agent-Based Architecture:** The system is decomposed into specialized, single-responsibility agents. This isolates complexity and allows each component to excel at its specific task.
-   **Hot/Cold Path Segregation:** We strictly separate low-latency, in-memory data (Redis) from durable, disk-based storage (Supabase). This allows the system to be extremely fast for real-time operations without sacrificing the ability to perform deep, historical analysis.
-   **Event-Driven Communication (Pub/Sub):** Instead of calling each other directly, agents publish events to a central message bus (Redis). This decouples the components, preventing a slow consumer from blocking a fast producer and making the system more modular and resilient.
-   **Dynamic Configuration (`Threshold Manager`):** System parameters are not hard-coded but are stored centrally in Redis. This allows for real-time tuning and control via our Discord bot, making the system adaptable without requiring code changes or redeployment.
-   **Containerization (Docker):** The entire application and its dependencies are packaged into a self-contained Docker image. This guarantees that the environment is 100% consistent from local development through to production.
-   **Process Supervisor:** Using Docker Compose with a `restart: always` policy provides a robust, automatic mechanism to keep all system processes running. It acts as our first line of defense against application crashes.
-   **Infrastructure as Code (IaC):** The entire server setup is defined declaratively in the `docker-compose.yml` file. This makes the infrastructure reproducible, version-controllable, and easy to modify or move to a new host.

---

## **3. Tech Stack**

| Category | Technology | Version | Purpose | Rationale |
| :--- | :--- | :--- | :--- | :--- |
| **Language** | TypeScript | 5.4.x | Core application logic, agent development, type safety. | End-to-end type safety, modern language features, strong ecosystem. |
| **Runtime** | Node.js | 20.x (LTS) | Executes the compiled JavaScript code. | High-performance, non-blocking I/O, standard for TypeScript applications. |
| **Framework** | LangGraph | latest | Orchestration of AI agents and state management. | User-specified choice; LangGraph.js provides a native TS/JS interface. |
| **Database** | PostgreSQL | 15.x | Durable storage for trades, signals, analytics (Cold Path). | Robust, reliable SQL database provided by the Supabase platform. |
| **Cache / Broker** | Redis | 7.2.x | High-speed state, messaging, and job queue (Hot Path). | Industry standard for low-latency in-memory data operations. |
| **Testing** | Vitest | 1.x | Unit and integration testing for all TypeScript code. | Modern, fast, and simple test framework with native TS support. |
| **Dependency Mgmt**| pnpm | 9.x | Dependency management and monorepo orchestration. | Fast, disk-space efficient, and excellent support for monorepos. |
| **IaC Tool** | Docker Compose | latest | Defining and running the multi-container application on the VPS. | Simplifies management of services, networking, and volumes on a single host. |
| **Logging** | Pino | 9.x | High-performance, structured application logging. | Extremely fast, produces structured JSON logs by default. |

---

## **4. Data Models**

This section defines the core data entities for the system, including PostgreSQL schema details and TypeScript interfaces.

### **Market**
- **Purpose:** Represents a financial instrument that the system is configured to trade.
- **TypeScript Interface:**
    ```typescript
    interface Market {
      id: number;
      symbol: string; // e.g., 'BTCUSDT'
      base_asset: string;
      quote_asset: string;
      is_active: boolean;
      created_at: Date;
    }
    ```

### **StrategyConfig**
- **Purpose:** Stores a versioned set of parameters for a specific trading strategy on a given market.
- **TypeScript Interface:**
    ```typescript
    interface MomentumV1Parameters {
      delta_threshold: number;
      trailing_stop_pct: number;
      imbalance_window_seconds: number;
      leverage: number;
      order_book_depth: number;
      liquidity_wall_pct: number;
    }

    interface StrategyConfig {
      id: number;
      market_id: number;
      strategy_name: string;
      version: number;
      parameters: MomentumV1Parameters;
      is_active: boolean;
      created_at: Date;
    }
    ```

### **Signal**
- **Purpose:** An immutable log of every significant decision or observation made by any agent.
- **TypeScript Interface:**
    ```typescript
    type Signal = { id: string; timestamp: Date; market_id: number; created_at: Date; } & (
      | { agent_name: 'MarketRegimeAgent'; signal_type: 'REGIME_PERMISSION'; signal_data: { permission: 'ALLOW_LONG' | 'ALLOW_SHORT' | 'BLOCK'; }; }
      | { agent_name: 'OrderFlowImbalanceAgent'; signal_type: 'ENTRY_SIGNAL'; signal_data: { direction: 'LONG' | 'SHORT'; price: number; delta: number; }; }
      | { agent_name: 'ExhaustionDetectionAgent'; signal_type: 'EXIT_SIGNAL'; signal_data: { reason: 'AGGRESSOR_FATIGUE' | 'PASSIVE_RESISTANCE' | 'TRAILING_STOP'; }; }
    );
    ```

### **Trade**
- **Purpose:** The master record for every executed trade, from open to close.
- **TypeScript Interface:**
    ```typescript
    interface Trade {
      id: string;
      market_id: number;
      strategy_config_id: number;
      status: 'OPEN' | 'CLOSED' | 'ERROR';
      direction: 'LONG' | 'SHORT';
      entry_timestamp: Date;
      exit_timestamp?: Date | null;
      entry_price: number;
      exit_price?: number | null;
      quantity: number;
      leverage: number;
      pnl?: number | null;
      pnl_percentage?: number | null;
      entry_signal_id?: string | null;
      exit_signal_id?: string | null;
      exit_reason?: string | null;
      created_at: Date;
      updated_at: Date;
    }
    ```

### **PerformanceSummary**
- **Purpose:** Stores a periodic, high-level summary of a strategy's performance, consumed by the `MasterAIAgent`.
- **TypeScript Interface:**
    ```typescript
    interface PerformanceSummary {
      id: string;
      strategy_config_id: number;
      period_start: Date;
      period_end: Date;
      total_trades: number;
      win_rate: number;
      pnl_percentage: number;
      sharpe_ratio: number;
      max_drawdown: number;
      parameters_used: MomentumV1Parameters;
      created_at: Date;
    }
    ```

---

## **5. Components**

This section provides a detailed engineering blueprint for each component.

### **MarketRegimeAgent**
-   **Component Type:** Scheduled, Strategic Agent.
-   **Responsibility:** Performs slow, strategic analysis of market conditions to set the overall trading policy.
-   **Core Logic:** Runs on a schedule, fetches OHLCV data, queries the LLM for a regime classification, and sets a permission key in Redis with a TTL as a fail-safe.

### **OrderFlowImbalanceAgent**
-   **Component Type:** Real-time, Stateless Stream Processor.
-   **Responsibility:** Performs high-frequency detection of order flow imbalances to generate entry signals.
-   **Core Logic:** Checks Redis for permission, subscribes to the live trade WebSocket stream, calculates a rolling Taker Volume Delta, and publishes an `ENTRY_SIGNAL` to Redis Pub/Sub if a threshold is breached.

### **ExhaustionDetectionAgent**
-   **Component Type:** Real-time, Stateful Stream Processor.
-   **Responsibility:** Monitors an open trade to detect signs of momentum exhaustion for an adaptive exit.
-   **Core Logic:** When a trade is active, it subscribes to both trade and order book (depth: 50) streams. If it detects either "aggressor fatigue" (slowing delta) or "passive resistance" (liquidity walls), it publishes an `EXIT_SIGNAL`.
-   **Failure Modes:** **Critical.** If this agent dies mid-trade, the system must recover via the Startup Reconciliation workflow.

### **ExecutionAgent**
-   **Component Type:** Reactive, Transactional Agent.
-   **Responsibility:** Manages the entire lifecycle of a trade by acting on signals.
-   **Core Logic:** Subscribes to signals. On entry, it performs pre-flight checks, gets position size from the GRM, and places market/stop-loss orders. On exit, it closes the position and writes the final record to Supabase. Includes startup reconciliation logic.

### **GlobalRiskManager (GRM)**
-   **Component Type:** Portfolio-level Supervisor.
-   **Responsibility:** Enforces portfolio-level risk rules like daily loss limits and concurrency control.
-   **Core Logic:** Periodically queries Supabase for P&L. If a risk limit is breached, it sets a global `HALTED` status in Redis. It also provides a function to calculate position size based on a fixed-fractional risk model.

### **MonitoringAgent & LoggerWorker**
-   **Component Type:** Asynchronous Utility Workers.
-   **Responsibility:** The `MonitoringAgent` sends human-readable notifications to Discord. The `LoggerWorker` asynchronously persists logs from the high-speed Redis queue to the durable Supabase database.

### **DiscordBot (Control Plane)**
-   **Component Type:** Human-in-the-Loop Interface.
-   **Responsibility:** Provides a secure interface for the operator to monitor and control the live system via Discord slash commands.

### **AnalyticsEngine**
-   **Component Type:** Scheduled, Batch Processor.
-   **Responsibility:** Periodically queries the raw `trades` data, calculates KPIs, and saves the results to the `performance_summaries` table.

### **MasterAIAgent**
-   **Component Type:** Scheduled, Strategic AI Agent.
-   **Responsibility:** Acts as an automated quantitative analyst, analyzing performance summaries and making small, data-driven adjustments to live trading parameters.
-   **Core Logic:** Runs after the `AnalyticsEngine`, reads the latest performance summary, queries the LLM for a parameter change suggestion, and then (in V1) presents this suggestion to the operator in Discord for manual approval.

---

## **6. External APIs**

This section provides detailed specifications for integrating with third-party services.

### **Binance (USDⓈ-M Futures) API (via CCXT Pro)**
-   **Purpose:** The primary interface to the market for both real-time data and trade execution. The integration uses a dual approach.
-   **Usage Pattern 1: Real-Time Data Streams (WebSocket):**
    -   Used by `OrderFlowImbalanceAgent` and `ExhaustionDetectionAgent` for `watchTrades('BTC/USDT:USDT')` and `watchOrderBook('BTC/USDT:USDT', 50)`.
-   **Usage Pattern 2: Command & Control (REST API):**
    -   Used by `ExecutionAgent` and `MarketRegimeAgent` for `createOrder(...)`, `cancelOrder(...)`, `fetchBalance()`, and `fetchOHLCV(...)`.
-   **Resilience:** Requires robust management of WebSocket reconnections and error handling for all REST API calls.

### **LLM API (Grok)**
-   **Purpose:** Provides high-level analytical capabilities for strategic agents.
-   **Authentication:** API Key.
-   **Failure Strategy:** All API calls must have an aggressive timeout. On failure or invalid response, the system must default to a "safe" state (e.g., blocking trades).

### **Discord API**
-   **Purpose:** Serves as the system's primary interface for human monitoring and control.
-   **Integration:** Uses both simple Webhooks for notifications and the full Bot User API for interactive slash commands.
-   **Security:** Requires role-based access control to restrict modification commands to the `SystemOperator` role.

### **Supabase Platform**
-   **Purpose:** Provides the hosted PostgreSQL database for "cold path" data storage.
-   **Authentication:** Direct database connection string.
-   **Connection Management:** A connection pool must be implemented to manage database connections efficiently.
-   **Security:** The Supabase project must be configured with network restrictions, allowing connections only from the known IP address of the trading VPS.

---

## **7. Core Workflows**

This section illustrates the primary operational flows of the system using UML Sequence Diagrams.

#### **Scenario 1: Successful Long Trade (Happy Path)**
```mermaid
sequenceDiagram
    participant MA as MarketRegimeAgent
    participant IA as OrderFlowImbalanceAgent
    participant EA as ExecutionAgent
    participant EDA as ExhaustionDetectionAgent
    participant GRM as GlobalRiskManager
    participant Redis
    participant Exchange
    participant Supabase

    MA->>Exchange: Fetch historical data
    MA->>Redis: SET market:permission = "ALLOW_LONG"

    loop Real-time Trade Stream
        IA->>Exchange: watchTrades()
        IA->>Redis: GET market:permission
        Redis-->>IA: "ALLOW_LONG"
    end
    Note right of IA: Detects bullish imbalance!
    IA->>Redis: PUBLISH signals (ENTRY_SIGNAL)

    EA->>Redis: Receives ENTRY_SIGNAL
    EA->>GRM: Request Position Size
    GRM-->>EA: Return calculated size
    EA->>Exchange: CREATE MARKET BUY Order
    EA->>Exchange: CREATE TRAILING STOP Order
    Exchange-->>EA: Order Confirmations
    EA->>Redis: SET trade:status = "OPEN"

    loop Real-time Monitoring
        EDA->>Redis: GET trade:status
        Redis-->>EDA: "OPEN"
        EDA->>Exchange: watchOrderBook()
    end
    Note right of EDA: Detects exhaustion!
    EDA->>Redis: PUBLISH signals (EXIT_SIGNAL)

    EA->>Redis: Receives EXIT_SIGNAL
    EA->>Exchange: CREATE MARKET SELL Order
    Exchange-->>EA: Close Confirmation
    EA->>Redis: DEL trade:status
    EA->>Supabase: INSERT final trade record into 'trades' table
```

---

#### **Scenario 2: Trade Blocked by Global Risk Manager (Safety Override)**
```mermaid
sequenceDiagram
    participant GRM as GlobalRiskManager
    participant IA as OrderFlowImbalanceAgent
    participant EA as ExecutionAgent
    participant Redis
    participant Supabase

    Note over GRM,Supabase: GRM periodically checks P&L from Supabase.
    GRM->>Supabase: SELECT SUM(pnl) FROM trades...
    Note over GRM: Max Drawdown Breached!
    GRM->>Redis: SET global:status:trading = "HALTED_MAX_DRAWDOWN"

    Note over IA,Redis: Later, a valid entry signal is found.
    IA->>Redis: PUBLISH signals (ENTRY_SIGNAL)

    EA->>Redis: Receives ENTRY_SIGNAL
    Note over EA: Performing pre-flight safety checks...
    EA->>Redis: GET global:status:trading
    Redis-->>EA: "HALTED_MAX_DRAWDOWN"
    
    Note over EA: Trade is BLOCKED.
    EA->>Redis: LPUSH queue:logging (Log "Trade Blocked by GRM")
```

---

#### **Scenario 3: Trade Exits via Trailing Stop-Loss (Alternative Exit)**
```mermaid
sequenceDiagram
    participant EA as ExecutionAgent
    participant Exchange
    participant Redis
    participant Supabase

    Note over EA,Exchange: A trade is open and its trailing stop is live on the exchange.
    Note over Exchange: Price moves against the position...
    Exchange->>Exchange: Trailing Stop-Loss is triggered and executed.
    
    Note over EA,Exchange: The ExecutionAgent receives the fill notification.
    Exchange-->>EA: WebSocket notification: STOP_ORDER_FILLED
    
    EA->>EA: Reconcile position state.
    EA->>Redis: DEL trade:status
    EA->>Supabase: INSERT final trade record with exit_reason = 'TRAILING_STOP'
```

---

#### **Scenario 4: Human Operator Halts Trading via Discord**
```mermaid
sequenceDiagram
    participant Operator as You
    participant Discord
    participant Bot as DiscordBot Component
    participant Redis

    Operator->>Discord: /toggle_strategy asset:BTCUSDT state:off
    Discord->>Bot: Sends Interaction event
    
    Bot->>Bot: Validate command & user permissions
    Bot->>Redis: HSET config:MomentumV1:BTCUSDT is_active "false"
    Redis-->>Bot: OK
    
    Bot->>Discord: Send confirmation message: "Strategy for BTCUSDT has been DISABLED."
    Discord-->>Operator: Display confirmation
```

---
#### **Scenario 5: Startup & State Reconciliation (MUST-FIX)**
```mermaid
sequenceDiagram
    participant App as Application
    participant EA as ExecutionAgent
    participant Exchange
    participant Supabase
    participant EDA as ExhaustionDetectionAgent

    App->>EA: Start Process
    EA->>Exchange: fetchOpenPositions()
    Exchange-->>EA: Returns list of open positions
    
    loop For each open position
        EA->>Supabase: SELECT * FROM trades WHERE exchange_trade_id = [pos_id] AND status = 'OPEN'
        Supabase-->>EA: Returns trade record or null
        
        alt Trade record exists in DB
            EA->>EA: Re-hydrate state for this trade
            EA->>EDA: Start monitoring this position
        else Trade record does NOT exist (Critical Error)
            EA->>Exchange: CREATE MARKET CLOSE Order for position
            EA->>Redis: LPUSH queue:logging (Log CRITICAL error: "Orphan position closed")
        end
    end
    
    Note over EA: Reconciliation complete.
    EA->>Redis: Start listening to signals channel
```
---
#### **Scenario 6: Automated Optimization Loop**
```mermaid
sequenceDiagram
    participant Scheduler
    participant Analytics as AnalyticsEngine
    participant MasterAI as MasterAIAgent
    participant Supabase
    participant LLM
    participant Redis
    participant Discord

    Scheduler->>Analytics: Trigger Daily Run
    Analytics->>Supabase: Query raw trades from `trades` table
    Supabase-->>Analytics: Return trade data
    Analytics->>Supabase: INSERT summary into `performance_summaries`
    
    Scheduler->>MasterAI: Trigger Optimization Run
    MasterAI->>Supabase: Read latest from `performance_summaries`
    Supabase-->>MasterAI: Return KPIs and parameters
    MasterAI->>LLM: Send structured prompt for analysis
    LLM-->>MasterAI: Return JSON with parameter change
    
    MasterAI->>Redis: HSET config hash with new parameter value
    MasterAI->>Discord: Send notification of the automated change
```

---

## **8. Database Schema**

This section provides the definitive SQL Data Definition Language (DDL) for creating the required tables in the Supabase PostgreSQL database.

```sql
-- Defines the financial instruments available for trading.
CREATE TABLE markets (
    id SERIAL PRIMARY KEY,
    symbol TEXT NOT NULL UNIQUE,
    base_asset TEXT NOT NULL,
    quote_asset TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Stores versioned configurations and parameters for trading strategies.
CREATE TABLE strategy_configs (
    id SERIAL PRIMARY KEY,
    market_id INTEGER NOT NULL REFERENCES markets(id) ON DELETE CASCADE,
    strategy_name TEXT NOT NULL,
    version INTEGER NOT NULL,
    parameters JSONB NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(market_id, strategy_name, version)
);

-- A comprehensive, immutable log of every decision or observation made by any agent.
CREATE TABLE signals (
    id BIGSERIAL PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL,
    market_id INTEGER NOT NULL REFERENCES markets(id) ON DELETE RESTRICT,
    agent_name TEXT NOT NULL,
    signal_type TEXT NOT NULL,
    signal_data JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_signals_timestamp ON signals(timestamp);
CREATE INDEX idx_signals_market_id_timestamp ON signals(market_id, timestamp);

-- The master record of all executed trades.
CREATE TABLE trades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    market_id INTEGER NOT NULL REFERENCES markets(id) ON DELETE RESTRICT,
    strategy_config_id INTEGER NOT NULL REFERENCES strategy_configs(id) ON DELETE RESTRICT,
    status TEXT NOT NULL CHECK (status IN ('OPEN', 'CLOSED', 'ERROR')),
    direction TEXT NOT NULL CHECK (direction IN ('LONG', 'SHORT')),
    entry_timestamp TIMESTAMPTZ NOT NULL,
    exit_timestamp TIMESTAMPTZ,
    entry_price NUMERIC NOT NULL,
    exit_price NUMERIC,
    quantity NUMERIC NOT NULL,
    leverage NUMERIC NOT NULL DEFAULT 10.0,
    entry_signal_id BIGINT REFERENCES signals(id) ON DELETE SET NULL,
    exit_signal_id BIGINT REFERENCES signals(id) ON DELETE SET NULL,
    exit_reason TEXT,
    pnl NUMERIC,
    pnl_percentage NUMERIC,
    exchange_trade_id TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_trades_status ON trades(status);
CREATE INDEX idx_trades_market_id_entry_timestamp ON trades(market_id, entry_timestamp);

-- Stores structured application logs for debugging and auditing.
CREATE TABLE logs (
    id BIGSERIAL PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    log_level TEXT NOT NULL CHECK (log_level IN ('DEBUG', 'INFO', 'WARN', 'ERROR', 'CRITICAL')),
    message TEXT NOT NULL,
    metadata JSONB
);
CREATE INDEX idx_logs_timestamp ON logs(timestamp);

-- Stores periodic KPI summaries for the Master AI Agent.
CREATE TABLE performance_summaries (
    id BIGSERIAL PRIMARY KEY,
    strategy_config_id INTEGER NOT NULL REFERENCES strategy_configs(id),
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    total_trades INTEGER NOT NULL,
    win_rate NUMERIC,
    pnl_percentage NUMERIC,
    sharpe_ratio NUMERIC,
    max_drawdown NUMERIC,
    parameters_used JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_perf_summary_strategy_period ON performance_summaries(strategy_config_id, period_end);

-- Utility function to automatically update 'updated_at' timestamps.
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_timestamp_trades
BEFORE UPDATE ON trades
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
```

---

## **9. Source Tree**

The project will be structured as a **monorepo managed by `pnpm` workspaces**.

#### **Directory Structure**
```plaintext
autonomous-trading-system/
├── .github/
│   └── workflows/
│       └── deploy.yml
├── apps/
│   └── trading-app/
│       ├── src/
│       │   ├── agents/
│       │   ├── components/
│       │   ├── services/
│       │   ├── workers/
│       │   ├── main.ts
│       │   └── config.ts
│       ├── tests/
│       ├── Dockerfile
│       └── package.json
├── packages/
│   ├── config/
│   │   ├── eslint/
│   │   └── tsconfig/
│   ├── shared-types/
│   │   ├── src/
│   │   └── package.json
│   └── utils/
│       ├── src/
│       └── package.json
├── .env.example
├── docker-compose.yml
├── package.json
├── pnpm-workspace.yaml
└── tsconfig.json
```

#### **Package Responsibilities**
*   **`apps/trading-app`**: The only deployable application, containing all agent logic and the main entry point.
*   **`packages/config`**: Contains shared ESLint and TypeScript configurations to enforce consistent code quality.
*   **`packages/shared-types`**: The "data contract" defining all TypeScript interfaces for database models, API responses, and Redis structures.
*   **`packages/utils`**: A shared "toolbox" for common utility functions to prevent code duplication.

---

## **10. In-Memory Data Store (Redis)**

This section defines the structure and conventions for the "hot path" data stored in Redis. A consistent key naming convention is used: `[context]:[asset_symbol]:[type]`.

#### **Redis Structures**

| Role | Redis Data Type | Example Key / Channel | Producer Agent(s) | Consumer Agent(s) | Purpose |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **State: Permission** | String | `permission:BTCUSDT:momentum` | MarketRegimeAgent | OrderFlowImbalanceAgent | Grants/revokes trading permission. Set with a TTL as a fail-safe. |
| **State: Trade Lock** | String | `status:BTCUSDT:trade` | ExecutionAgent | ExecutionAgent, EDA | Prevents concurrent trades and activates the `ExhaustionDetectionAgent`. |
| **State: Global Lock** | String | `status:global:trading` | GlobalRiskManager | ExecutionAgent | Halts all new trading system-wide (e.g., on max drawdown). |
| **Messaging: Signals** | Pub/Sub Channel | `signals:BTCUSDT` | Imbalance, Exhaustion | ExecutionAgent, Monitoring | Real-time, fire-and-forget signals for entry and exit. |
| **Queue: Logging** | List | `queue:logging` | All Agents | LoggerWorker | A high-speed buffer for log jobs to be persisted asynchronously. |
| **Config: Parameters**| Hash | `config:MomentumV1:BTCUSDT` | DiscordBot, Master AI | All Trading Agents | Stores live, tunable strategy parameters (`Threshold Manager`). |

