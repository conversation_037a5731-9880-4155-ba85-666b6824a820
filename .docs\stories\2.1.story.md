# Story 2.1: MarketRegimeAgent Implementation

## Status: Draft

## Story

- As a **System Operator**
- I want **the system to perform high-level market analysis using AI**
- so that **it only attempts to trade when market conditions are suitable for the momentum strategy**

## Acceptance Criteria (ACs)

1. **AC 1.1 (Scheduling):** The `MarketRegimeAgent` **must** run on a configurable schedule, defaulting to every **15 minutes**
2. **AC 1.2 (Data Fetching):** On each run, it **must** fetch the last 4 hours of OHLCV data for the `BTCUSDT` pair from the Binance API
3. **AC 1.3 (LLM Prompting):** It **must** construct a prompt for the Grok LLM to classify the current regime and to output a structured JSON response
4. **AC 1.4 (State Writing):** It **must** set the Redis key `permission:BTCUSDT:momentum` to `"ALLOW_LONG"`, `"ALLOW_SHORT"`, or `"BLOCK"`
5. **AC 1.5 (Fail-Safe):** The Redis key **must** be set with a Time-To-Live (TTL) of **20 minutes**
6. **AC 1.6 (<PERSON><PERSON><PERSON>ling):** In the event of an LLM API failure or an unparsable response, the agent **must** log a critical error and default to setting the permission to `"BLOCK"`

## Tasks / Subtasks

- [ ] Task 1: Create MarketRegimeAgent Class Structure (AC: 1.1)
  - [ ] Create apps/trading-app/src/agents/MarketRegimeAgent.ts extending ScheduledAgent
  - [ ] Implement configurable scheduling with 15-minute default interval
  - [ ] Set up agent lifecycle management and health monitoring
  - [ ] Configure agent registration with LangGraph orchestration

- [ ] Task 2: Implement Binance Data Fetching (AC: 1.2)
  - [ ] Integrate CCXT Pro for Binance API connectivity
  - [ ] Implement fetchOHLCV() for BTCUSDT pair with 4-hour lookback
  - [ ] Add error handling and retry logic for API failures
  - [ ] Configure rate limiting and connection management

- [ ] Task 3: Build Grok LLM Integration (AC: 1.3)
  - [ ] Create LLM service wrapper for Grok API calls
  - [ ] Design market regime classification prompt template
  - [ ] Implement structured JSON response parsing and validation
  - [ ] Add aggressive timeout handling (30 seconds max)

- [ ] Task 4: Implement Redis Permission Management (AC: 1.4, 1.5)
  - [ ] Create permission key management using Redis string operations
  - [ ] Implement TTL setting for 20-minute fail-safe expiration
  - [ ] Add permission state validation and logging
  - [ ] Ensure atomic operations for permission updates

- [ ] Task 5: Add Comprehensive Error Handling (AC: 1.6)
  - [ ] Implement fail-safe logic defaulting to "BLOCK" on any error
  - [ ] Add critical error logging for LLM failures
  - [ ] Create circuit breaker pattern for repeated failures
  - [ ] Implement graceful degradation strategies

- [ ] Task 6: Configuration and Testing Integration
  - [ ] Load agent parameters from ThresholdManager
  - [ ] Add configuration validation and runtime updates
  - [ ] Implement comprehensive logging and monitoring
  - [ ] Create unit and integration tests for all functionality

## Dev Notes

### Technical Guidance

**Agent Type and Responsibility** [Source: architecture/components.md#MarketRegimeAgent]:
- Component Type: Scheduled, Strategic Agent
- Responsibility: Performs slow, strategic analysis of market conditions to set overall trading policy
- Core Logic: Runs on schedule, fetches OHLCV data, queries LLM for regime classification, sets permission key in Redis with TTL fail-safe

**Binance API Integration** [Source: architecture/external-apis.md#Binance]:
- Use CCXT Pro for REST API calls: `fetchOHLCV(...)` for historical data
- MarketRegimeAgent uses Command & Control pattern (REST API, not WebSocket)
- Requires robust error handling for all REST API calls
- Must handle rate limiting and connection management

**Grok LLM API Requirements** [Source: architecture/external-apis.md#LLM]:
- Purpose: High-level analytical capabilities for strategic agents
- Authentication: API Key
- Failure Strategy: Aggressive timeout, default to safe state ("BLOCK") on failure
- Must handle unparsable responses gracefully

**Redis Permission Management** [Source: architecture/in-memory-data-store-redis.md]:
- Key Pattern: `permission:BTCUSDT:momentum` (String with TTL)
- Values: `"ALLOW_LONG"`, `"ALLOW_SHORT"`, or `"BLOCK"`
- TTL: 20 minutes as fail-safe mechanism
- Producer: MarketRegimeAgent, Consumer: OrderFlowImbalanceAgent

**File Locations** [Source: architecture/source-tree.md]:
- Agent implementation: `apps/trading-app/src/agents/MarketRegimeAgent.ts`
- Base class: `apps/trading-app/src/agents/base/ScheduledAgent.ts` (from Story 1.4)
- LLM service: `apps/trading-app/src/services/llm.ts`
- Binance service: `apps/trading-app/src/services/binance.ts`

**Technical Constraints**:
- Must extend ScheduledAgent base class from Story 1.4
- 15-minute default schedule with configurable intervals
- 4-hour OHLCV data lookback for regime analysis
- 30-second maximum timeout for LLM API calls
- Fail-safe default to "BLOCK" on any error condition

### Previous Story Context

Epic 1 (Stories 1.1-1.4) established the complete foundation:
- **Story 1.1:** Monorepo structure and development environment
- **Story 1.2:** Database schema and Supabase integration
- **Story 1.3:** Redis infrastructure and ThresholdManager
- **Story 1.4:** Agent framework with ScheduledAgent base class

The MarketRegimeAgent builds on this foundation by:
- Extending the ScheduledAgent base class for consistent lifecycle management
- Using Redis infrastructure for permission state management
- Leveraging ThresholdManager for configuration loading
- Following established logging and error handling patterns

### Testing

Dev Note: Story Requires the following tests:

- [x] Vitest Unit Tests: (nextToFile: true), coverage requirement: 80%
- [x] Integration Test: location: `apps/trading-app/tests/agents/market-regime-agent.integration.test.ts`
- [ ] E2E: location: `/e2e/agents/market-regime-analysis.test.ts`

Manual Test Steps:
- Start MarketRegimeAgent and verify 15-minute scheduling works
- Test OHLCV data fetching from Binance API for BTCUSDT
- Verify Grok LLM integration with structured JSON response parsing
- Test Redis permission key setting with proper TTL (20 minutes)
- Simulate LLM API failures and verify "BLOCK" default behavior
- Test configuration loading from ThresholdManager
- Verify agent integrates properly with LangGraph orchestration

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
