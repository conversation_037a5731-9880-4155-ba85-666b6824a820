# High Level Architecture

## Technical Summary

The Autonomous Trading System is an event-driven, agent-based application architected for high performance and operational maturity. It is deployed as a set of containerized services on a dedicated VPS using Docker and Docker Compose. The core design principle is a strict separation of the **hot data path** from the **cold data path**. The hot path handles real-time, low-latency state and inter-agent messaging using **Redis**. The cold data path ensures durable, analytical storage of all trades, signals, and logs in a **Supabase PostgreSQL** database. This architecture is designed for reliable, automated deployment via a standard CI/CD pipeline and includes a meta-learning feedback loop for future self-optimization.

## C4-Style Container Diagram

This diagram provides a detailed view of the containers running within the VPS and their interactions.

```mermaid
graph TD
    subgraph "External Systems"
        A[Operator]
        B[Binance USDⓈ-M Futures]
        C[LLM API (Grok)]
        D[Supabase Platform]
        E[Discord API]
    end

    subgraph "Your VPS"
        direction LR
        subgraph "Docker Environment"
            boundary[Container Boundary]

            subgraph "Container: trading-app"
                TA[LangGraph Agents]
                TA_API[Discord Command API]
            end

            subgraph "Container: logger-worker"
                LW[Logger Process]
            end

            subgraph "Container: redis"
                R[(Redis)]
            end
        end
    end

    %% User Interaction Flow
    A -- "/slash commands via HTTPS" --> E
    E -- "Webhook/API Call" --> TA_API

    %% Hot Path Trading Flow
    TA -- "Streams market data & places orders via WebSocket/REST" --> B
    TA -- "Reads/writes state & Pub/Sub messages" --> R

    %% Cold Path & Strategic Flow
    TA -- "Makes API calls for analysis" --> C
    TA -- "Pushes log jobs to queue" --> R
    LW -- "Pops log jobs from queue" --> R
    LW -- "Writes logs to database" --> D
    TA -- "Writes final trade records" --> D
```

## Architectural and Design Patterns

-   **Agent-Based Architecture:** The system is decomposed into specialized, single-responsibility agents. This isolates complexity and allows each component to excel at its specific task.
-   **Hot/Cold Path Segregation:** We strictly separate low-latency, in-memory data (Redis) from durable, disk-based storage (Supabase). This allows the system to be extremely fast for real-time operations without sacrificing the ability to perform deep, historical analysis.
-   **Event-Driven Communication (Pub/Sub):** Instead of calling each other directly, agents publish events to a central message bus (Redis). This decouples the components, preventing a slow consumer from blocking a fast producer and making the system more modular and resilient.
-   **Dynamic Configuration (`Threshold Manager`):** System parameters are not hard-coded but are stored centrally in Redis. This allows for real-time tuning and control via our Discord bot, making the system adaptable without requiring code changes or redeployment.
-   **Containerization (Docker):** The entire application and its dependencies are packaged into a self-contained Docker image. This guarantees that the environment is 100% consistent from local development through to production.
-   **Process Supervisor:** Using Docker Compose with a `restart: always` policy provides a robust, automatic mechanism to keep all system processes running. It acts as our first line of defense against application crashes.
-   **Infrastructure as Code (IaC):** The entire server setup is defined declaratively in the `docker-compose.yml` file. This makes the infrastructure reproducible, version-controllable, and easy to modify or move to a new host.
