# Story 2.2: OrderFlowImbalanceAgent Implementation

## Status: Draft

## Story

- As a **System Operator**
- I want **the system to monitor real-time trade data and detect powerful, one-sided order flow**
- so that **it can generate timely entry signals when momentum opportunities arise**

## Acceptance Criteria (ACs)

1. **AC 2.1 (Permission Check):** The `OrderFlowImbalanceAgent` **must** remain idle if the Redis permission key is `"BLOCK"`
2. **AC 2.2 (Data Streaming):** It **must** maintain a persistent WebSocket connection to the Binance `watchTrades()` stream
3. **AC 2.3 (Delta Calculation):** It **must** calculate a rolling Taker Volume Delta over a configurable time window (default: 3 seconds)
4. **AC 2.4 (Threshold Breach):** If the delta surpasses the configured threshold, it **must** immediately publish an `ENTRY_SIGNAL` message
5. **AC 2.5 (Signal Publishing):** The message **must** be published to the Redis Pub/Sub channel `signals:BTCUSDT`

## Tasks / Subtasks

- [ ] Task 1: Create OrderFlowImbalanceAgent Class Structure (AC: 2.1)
  - [ ] Create apps/trading-app/src/agents/OrderFlowImbalanceAgent.ts extending RealtimeAgent
  - [ ] Implement permission checking logic using Redis `permission:BTCUSDT:momentum` key
  - [ ] Add idle state management when permission is "BLOCK"
  - [ ] Configure agent registration with LangGraph orchestration

- [ ] Task 2: Implement Binance WebSocket Integration (AC: 2.2)
  - [ ] Integrate CCXT Pro for Binance WebSocket connectivity
  - [ ] Implement persistent `watchTrades('BTC/USDT:USDT')` stream connection
  - [ ] Add robust WebSocket reconnection logic and error handling
  - [ ] Configure connection monitoring and health checks

- [ ] Task 3: Build Rolling Taker Volume Delta Calculation (AC: 2.3)
  - [ ] Implement sliding window data structure for trade aggregation
  - [ ] Calculate Taker Volume Delta: (Buy Volume - Sell Volume) over time window
  - [ ] Add configurable time window parameter (default: 3 seconds)
  - [ ] Optimize for high-frequency real-time processing

- [ ] Task 4: Implement Threshold Detection & Signal Generation (AC: 2.4)
  - [ ] Load delta threshold parameter from ThresholdManager
  - [ ] Implement threshold breach detection logic
  - [ ] Generate ENTRY_SIGNAL with direction (LONG/SHORT), price, and delta
  - [ ] Add signal validation and error handling

- [ ] Task 5: Create Redis Pub/Sub Signal Publishing (AC: 2.5)
  - [ ] Implement signal publishing to `signals:BTCUSDT` channel
  - [ ] Create structured signal message format matching Signal interface
  - [ ] Add signal serialization and publishing utilities
  - [ ] Implement publishing error handling and retry logic

- [ ] Task 6: Configuration and Performance Optimization
  - [ ] Load all agent parameters from ThresholdManager
  - [ ] Implement runtime configuration updates
  - [ ] Optimize for sub-second signal processing performance
  - [ ] Add comprehensive logging and monitoring

## Dev Notes

### Technical Guidance

**Agent Type and Responsibility** [Source: architecture/components.md#OrderFlowImbalanceAgent]:
- Component Type: Real-time, Stateless Stream Processor
- Responsibility: High-frequency detection of order flow imbalances to generate entry signals
- Core Logic: Checks Redis permission, subscribes to live trade WebSocket, calculates rolling Taker Volume Delta, publishes ENTRY_SIGNAL on threshold breach

**Binance WebSocket Integration** [Source: architecture/external-apis.md#Binance]:
- Use CCXT Pro for WebSocket streams: `watchTrades('BTC/USDT:USDT')`
- Real-Time Data Streams pattern (not REST API)
- Requires robust WebSocket reconnection management
- Must handle connection failures and stream interruptions

**Signal Data Structure** [Source: architecture/data-models.md#Signal]:
```typescript
{
  agent_name: 'OrderFlowImbalanceAgent';
  signal_type: 'ENTRY_SIGNAL';
  signal_data: {
    direction: 'LONG' | 'SHORT';
    price: number;
    delta: number;
  };
}
```

**Redis Integration Patterns** [Source: architecture/in-memory-data-store-redis.md]:
- Permission Check: Read `permission:BTCUSDT:momentum` (String)
- Signal Publishing: Publish to `signals:BTCUSDT` (Pub/Sub Channel)
- Consumer: ExecutionAgent subscribes to signals channel
- Producer: OrderFlowImbalanceAgent publishes entry signals

**Strategy Parameters** [Source: architecture/data-models.md#MomentumV1Parameters]:
- delta_threshold: number (threshold for order flow imbalance detection)
- imbalance_window_seconds: number (rolling calculation window, default: 3)
- Additional parameters loaded from ThresholdManager configuration

**File Locations** [Source: architecture/source-tree.md]:
- Agent implementation: `apps/trading-app/src/agents/OrderFlowImbalanceAgent.ts`
- Base class: `apps/trading-app/src/agents/base/RealtimeAgent.ts` (from Story 1.4)
- Binance service: `apps/trading-app/src/services/binance.ts`
- Signal utilities: `apps/trading-app/src/services/signals.ts`

**Technical Constraints**:
- Must extend RealtimeAgent base class from Story 1.4
- Sub-second processing requirement for real-time signals
- Stateless design for high-frequency operation
- 3-second default window for delta calculation
- Must remain idle when permission is "BLOCK"

### Previous Story Context

Epic 1 (Stories 1.1-1.4) and Story 2.1 provide the foundation:
- **Story 1.4:** RealtimeAgent base class and agent framework
- **Story 1.3:** Redis infrastructure for permission checks and signal publishing
- **Story 2.1:** MarketRegimeAgent sets permission keys that this agent reads

The OrderFlowImbalanceAgent builds on this foundation by:
- Extending RealtimeAgent base class for stream processing lifecycle
- Reading permission keys set by MarketRegimeAgent
- Using Redis Pub/Sub infrastructure for signal publishing
- Following established logging and error handling patterns

### Testing

Dev Note: Story Requires the following tests:

- [x] Vitest Unit Tests: (nextToFile: true), coverage requirement: 80%
- [x] Integration Test: location: `apps/trading-app/tests/agents/order-flow-imbalance-agent.integration.test.ts`
- [ ] E2E: location: `/e2e/agents/order-flow-detection.test.ts`

Manual Test Steps:
- Start OrderFlowImbalanceAgent and verify WebSocket connection to Binance
- Test permission checking with different Redis permission states
- Verify rolling Taker Volume Delta calculation with sample trade data
- Test threshold breach detection and ENTRY_SIGNAL generation
- Verify signal publishing to Redis `signals:BTCUSDT` channel
- Test WebSocket reconnection behavior during connection failures
- Validate sub-second processing performance under high trade volume

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
