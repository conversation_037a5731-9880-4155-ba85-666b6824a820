# Components

This section provides a detailed engineering blueprint for each component.

## MarketRegimeAgent
-   **Component Type:** Scheduled, Strategic Agent.
-   **Responsibility:** Performs slow, strategic analysis of market conditions to set the overall trading policy.
-   **Core Logic:** Runs on a schedule, fetches OHLCV data, queries the LLM for a regime classification, and sets a permission key in Redis with a TTL as a fail-safe.

## OrderFlowImbalanceAgent
-   **Component Type:** Real-time, Stateless Stream Processor.
-   **Responsibility:** Performs high-frequency detection of order flow imbalances to generate entry signals.
-   **Core Logic:** Checks Redis for permission, subscribes to the live trade WebSocket stream, calculates a rolling Taker Volume Delta, and publishes an `ENTRY_SIGNAL` to Redis Pub/Sub if a threshold is breached.

## ExhaustionDetectionAgent
-   **Component Type:** Real-time, Stateful Stream Processor.
-   **Responsibility:** Monitors an open trade to detect signs of momentum exhaustion for an adaptive exit.
-   **Core Logic:** When a trade is active, it subscribes to both trade and order book (depth: 50) streams. If it detects either "aggressor fatigue" (slowing delta) or "passive resistance" (liquidity walls), it publishes an `EXIT_SIGNAL`.
-   **Failure Modes:** **Critical.** If this agent dies mid-trade, the system must recover via the Startup Reconciliation workflow.

## ExecutionAgent
-   **Component Type:** Reactive, Transactional Agent.
-   **Responsibility:** Manages the entire lifecycle of a trade by acting on signals.
-   **Core Logic:** Subscribes to signals. On entry, it performs pre-flight checks, gets position size from the GRM, and places market/stop-loss orders. On exit, it closes the position and writes the final record to Supabase. Includes startup reconciliation logic.

## GlobalRiskManager (GRM)
-   **Component Type:** Portfolio-level Supervisor.
-   **Responsibility:** Enforces portfolio-level risk rules like daily loss limits and concurrency control.
-   **Core Logic:** Periodically queries Supabase for P&L. If a risk limit is breached, it sets a global `HALTED` status in Redis. It also provides a function to calculate position size based on a fixed-fractional risk model.

## MonitoringAgent & LoggerWorker
-   **Component Type:** Asynchronous Utility Workers.
-   **Responsibility:** The `MonitoringAgent` sends human-readable notifications to Discord. The `LoggerWorker` asynchronously persists logs from the high-speed Redis queue to the durable Supabase database.

## DiscordBot (Control Plane)
-   **Component Type:** Human-in-the-Loop Interface.
-   **Responsibility:** Provides a secure interface for the operator to monitor and control the live system via Discord slash commands.

## AnalyticsEngine
-   **Component Type:** Scheduled, Batch Processor.
-   **Responsibility:** Periodically queries the raw `trades` data, calculates KPIs, and saves the results to the `performance_summaries` table.

## MasterAIAgent
-   **Component Type:** Scheduled, Strategic AI Agent.
-   **Responsibility:** Acts as an automated quantitative analyst, analyzing performance summaries and making small, data-driven adjustments to live trading parameters.
-   **Core Logic:** Runs after the `AnalyticsEngine`, reads the latest performance summary, queries the LLM for a parameter change suggestion, and then (in V1) presents this suggestion to the operator in Discord for manual approval.
