# Story 3.1: GlobalRiskManager Implementation

## Status: Draft

## Story

- As a **System Operator**
- I want **a global risk manager to enforce portfolio-level rules to protect my capital**
- so that **the system prevents excessive losses through position sizing, drawdown limits, and emergency halt mechanisms**

## Acceptance Criteria (ACs)

1. **AC 5.1 (Position Sizing):** The GRM **must** calculate trade size based on a fixed-fractional model (e.g., risk 1% of equity).
2. **AC 5.2 (Daily Drawdown Limit):** The GRM **must** periodically calculate the daily P&L. If it exceeds the configured limit (e.g., 3%), it **must** halt all new trading.
3. **AC 5.3 (System Halt):** The halt is achieved by setting the `global:status:trading` key in Redis to a HALTED state with a 24-hour TTL.
4. **AC 5.4 (Concurrency Control):** The system **must** not open more than the configured maximum number of concurrent trades (default: 1).

## Tasks / Subtasks

- [ ] Task 1: Implement Core GlobalRiskManager Class (AC: 5.1, 5.4)
  - [ ] Create GlobalRiskManager component in `apps/trading-app/src/agents/`
  - [ ] Implement fixed-fractional position sizing calculation method
  - [ ] Add concurrency control validation (check Redis trade status keys)
  - [ ] Integrate with ThresholdManager for risk parameter loading
  - [ ] Add comprehensive error handling and logging

- [ ] Task 2: Implement Daily P&L Monitoring (AC: 5.2)
  - [ ] Create periodic P&L calculation from Supabase trades table
  - [ ] Implement daily drawdown limit checking logic
  - [ ] Add P&L aggregation queries with proper date filtering
  - [ ] Create risk assessment reporting functionality
  - [ ] Add configurable monitoring intervals

- [ ] Task 3: Implement Global Trading Halt Mechanism (AC: 5.3)
  - [ ] Create Redis global status management (`global:status:trading`)
  - [ ] Implement halt trigger with 24-hour TTL fail-safe
  - [ ] Add halt reason tracking and logging
  - [ ] Create manual halt/resume functionality
  - [ ] Add emergency halt scenarios handling

- [ ] Task 4: Integration with ExecutionAgent
  - [ ] Modify ExecutionAgent to call GRM for position sizing
  - [ ] Add pre-flight safety checks integration
  - [ ] Implement global status checking in trade workflow
  - [ ] Add GRM validation to signal processing
  - [ ] Test complete integration workflow

- [ ] Task 5: Database Integration & Risk Calculations
  - [ ] Implement Supabase connection for P&L queries
  - [ ] Create efficient trade aggregation queries
  - [ ] Add portfolio value calculation methods
  - [ ] Implement risk metrics calculation (drawdown, exposure)
  - [ ] Add data validation and error handling

## Dev Technical Guidance

### Previous Story Insights
No previous story completion notes available as Story 2.4 is still in Draft status.

### Data Models [Source: architecture/data-models.md]
**Trade Interface:**
```typescript
interface Trade {
  id: string; market_id: number; strategy_config_id: number;
  status: 'OPEN' | 'CLOSED' | 'ERROR'; direction: 'LONG' | 'SHORT';
  entry_timestamp: Date; exit_timestamp?: Date | null;
  entry_price: number; exit_price?: number | null;
  quantity: number; leverage: number; pnl?: number | null;
  pnl_percentage?: number | null;
  entry_signal_id?: string | null; exit_signal_id?: string | null;
  exit_reason?: string | null; created_at: Date; updated_at: Date;
}
```

### Redis Integration Patterns [Source: architecture/in-memory-data-store-redis.md]
- **Global Status Management:** Set/Get `global:status:trading` (String) with TTL
- **Trade Status Checking:** Check `status:BTCUSDT:trade` for concurrency control
- **Parameter Loading:** Load from `config:MomentumV1:BTCUSDT` (Hash)

### Component Specifications [Source: architecture/components.md]
**GlobalRiskManager (GRM):**
- Component Type: Portfolio-level Supervisor
- Responsibility: Enforces portfolio-level risk rules like daily loss limits and concurrency control
- Core Logic: Periodically queries Supabase for P&L. If risk limit breached, sets global HALTED status in Redis. Provides position size calculation using fixed-fractional risk model.

### File Locations [Source: architecture/source-tree.md]
- **Main Implementation:** `apps/trading-app/src/agents/GlobalRiskManager.ts`
- **Shared Types:** `packages/shared-types/src/` for interfaces
- **Utils:** `packages/utils/src/` for common risk calculation functions
- **Tests:** `apps/trading-app/tests/agents/GlobalRiskManager.test.ts`

### Core Workflows Integration [Source: architecture/core-workflows.md]
**Scenario 1 - Position Sizing:**
1. ExecutionAgent receives ENTRY_SIGNAL
2. EA calls GRM.calculatePositionSize()
3. GRM returns calculated size based on fixed-fractional model
4. EA proceeds with trade execution

**Scenario 2 - Risk Halt:**
1. GRM periodically checks P&L from Supabase
2. If max drawdown breached, GRM sets `global:status:trading = "HALTED_MAX_DRAWDOWN"`
3. ExecutionAgent checks global status during pre-flight
4. Trade is blocked if status is HALTED

### Technical Constraints [Source: architecture/tech-stack.md]
- **Language:** TypeScript 5.4.x with strict type safety
- **Runtime:** Node.js 20.x LTS
- **Database:** PostgreSQL 15.x via Supabase
- **Cache:** Redis 7.2.x for state management
- **Testing:** Vitest 1.x for unit and integration tests
- **Logging:** Pino 9.x for structured logging

### Testing Requirements
Dev Note: Story Requires the following tests:

- [ ] Vitest Unit Tests: (nextToFile: true), coverage requirement: 80%
- [ ] Vitest Integration Test: location: `apps/trading-app/tests/agents/GlobalRiskManager.integration.test.ts`
- [ ] Manual Test Steps: Create test script to simulate drawdown scenarios and verify halt mechanism

Manual Test Steps:
- Dev will create a script that simulates trades with losses exceeding daily limit
- Verify GRM halts trading by setting Redis global status
- Test position sizing calculation with different equity values
- Validate concurrency control prevents multiple open trades

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
