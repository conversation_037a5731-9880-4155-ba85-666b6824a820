# Story 2.4: ExecutionAgent Implementation

## Status: Draft

## Story

- As a **System Operator**
- I want **a dedicated agent to reliably execute orders and manage the complete trade lifecycle**
- so that **the system can automatically execute trades based on signals with proper risk management and state tracking**

## Acceptance Criteria (ACs)

1. **AC 4.1 (Signal Subscription):** The `ExecutionAgent` **must** subscribe to the `signals:BTCUSDT` channel
2. **AC 4.2 (Pre-flight Checks):** It **must** verify global and market-specific trading status in Redis before placing an order
3. **AC 4.3 (Order Placement):** It **must** place a market order with the configured leverage (default: 10x)
4. **AC 4.4 (Stop-Loss Placement):** Immediately after entry, it **must** place an exchange-side trailing stop-loss order (default: 0.15%)
5. **AC 4.5 (Database Writing):** After a trade is closed, it **must** write a complete record to the `trades` table in Supabase
6. **AC 4.6 (State Reconciliation on Startup):** On application startup, it **must** query Binance for any open positions and re-hydrate its state to manage them

## Tasks / Subtasks

- [ ] Task 1: Create ExecutionAgent Class Structure (AC: 4.1)
  - [ ] Create apps/trading-app/src/agents/ExecutionAgent.ts extending ReactiveAgent
  - [ ] Implement Redis Pub/Sub subscription to `signals:BTCUSDT` channel
  - [ ] Add signal message parsing and validation
  - [ ] Configure agent registration with LangGraph orchestration

- [ ] Task 2: Implement Pre-flight Safety Checks (AC: 4.2)
  - [ ] Create comprehensive pre-flight check system
  - [ ] Verify global trading status (`global:status:trading` in Redis)
  - [ ] Check market-specific trading permissions
  - [ ] Integrate with GlobalRiskManager for position sizing and limits
  - [ ] Add concurrency control validation (max 1 open trade)

- [ ] Task 3: Build Order Execution System (AC: 4.3, 4.4)
  - [ ] Integrate CCXT Pro for Binance order execution
  - [ ] Implement market order placement with configurable leverage
  - [ ] Add trailing stop-loss order placement immediately after entry
  - [ ] Create order confirmation and error handling
  - [ ] Implement order state tracking and management

- [ ] Task 4: Implement Trade State Management
  - [ ] Create Redis trade state management (`status:BTCUSDT:trade`)
  - [ ] Track trade lifecycle from entry to exit
  - [ ] Coordinate with ExhaustionDetectionAgent activation
  - [ ] Add trade state persistence and recovery

- [ ] Task 5: Build Database Integration & Trade Recording (AC: 4.5)
  - [ ] Create complete trade record writing to Supabase `trades` table
  - [ ] Include all trade details: entry/exit prices, P&L, signals, timestamps
  - [ ] Add trade performance calculation (P&L, percentage)
  - [ ] Implement database error handling and retry logic

- [ ] Task 6: Implement Startup State Reconciliation (AC: 4.6)
  - [ ] Query Binance for open positions on startup
  - [ ] Match positions with database trade records
  - [ ] Re-hydrate agent state for existing trades
  - [ ] Handle orphan positions (close positions without DB records)
  - [ ] Activate ExhaustionDetectionAgent for reconciled trades

## Dev Notes

### Technical Guidance

**Agent Type and Responsibility** [Source: architecture/components.md#ExecutionAgent]:
- Component Type: Reactive, Transactional Agent
- Responsibility: Manages entire lifecycle of a trade by acting on signals
- Core Logic: Subscribes to signals, performs pre-flight checks, gets position size from GRM, places market/stop-loss orders, closes positions, writes final records to Supabase, includes startup reconciliation

**Binance API Integration** [Source: architecture/external-apis.md#Binance]:
- Use CCXT Pro for REST API calls: `createOrder(...)`, `cancelOrder(...)`, `fetchBalance()`, `fetchOpenPositions()`
- Command & Control pattern (REST API, not WebSocket)
- Requires robust error handling for all REST API calls
- Must handle rate limiting and connection management

**Signal Processing** [Source: architecture/data-models.md#Signal]:
- Subscribes to `signals:BTCUSDT` Redis Pub/Sub channel
- Processes ENTRY_SIGNAL from OrderFlowImbalanceAgent
- Processes EXIT_SIGNAL from ExhaustionDetectionAgent
- Must validate signal structure and handle malformed messages

**Trade Data Model** [Source: architecture/data-models.md#Trade]:
```typescript
interface Trade {
  id: string; market_id: number; strategy_config_id: number;
  status: 'OPEN' | 'CLOSED' | 'ERROR'; direction: 'LONG' | 'SHORT';
  entry_timestamp: Date; exit_timestamp?: Date | null;
  entry_price: number; exit_price?: number | null;
  quantity: number; leverage: number; pnl?: number | null;
  entry_signal_id?: string | null; exit_signal_id?: string | null;
  exit_reason?: string | null;
}
```

**Redis Integration Patterns** [Source: architecture/in-memory-data-store-redis.md]:
- Signal Subscription: Subscribe to `signals:BTCUSDT` (Pub/Sub Channel)
- Trade State: Set/Delete `status:BTCUSDT:trade` (String)
- Global Status: Check `global:status:trading` (String)
- Configuration: Load parameters from ThresholdManager (Hash)

**Startup Reconciliation Workflow** [Source: architecture/core-workflows.md#Scenario5]:
1. Query Binance for open positions using `fetchOpenPositions()`
2. For each position, query Supabase for matching trade record
3. If trade record exists: re-hydrate state and activate ExhaustionDetectionAgent
4. If no trade record: close orphan position and log critical error
5. Start listening to signals channel after reconciliation complete

**File Locations** [Source: architecture/source-tree.md]:
- Agent implementation: `apps/trading-app/src/agents/ExecutionAgent.ts`
- Base class: `apps/trading-app/src/agents/base/ReactiveAgent.ts` (from Story 1.4)
- Binance service: `apps/trading-app/src/services/binance.ts`
- Database service: `apps/trading-app/src/services/database.ts`

**Technical Constraints**:
- Must extend ReactiveAgent base class from Story 1.4
- Default leverage: 10x (configurable via ThresholdManager)
- Default trailing stop: 0.15% (configurable)
- Maximum 1 concurrent trade (enforced by GlobalRiskManager)
- Critical startup reconciliation required for trade safety

### Previous Story Context

Epic 1 (Stories 1.1-1.4) and Stories 2.1-2.3 provide the complete foundation:
- **Story 1.4:** ReactiveAgent base class and agent framework
- **Story 1.2:** Database schema and Supabase integration for trade records
- **Story 1.3:** Redis infrastructure for signal subscription and state management
- **Story 2.1:** MarketRegimeAgent provides trading permissions
- **Story 2.2:** OrderFlowImbalanceAgent generates ENTRY_SIGNAL messages
- **Story 2.3:** ExhaustionDetectionAgent generates EXIT_SIGNAL messages

The ExecutionAgent is the central orchestrator that:
- Receives signals from OrderFlowImbalanceAgent and ExhaustionDetectionAgent
- Manages trade state that ExhaustionDetectionAgent monitors
- Uses database infrastructure for permanent trade record storage
- Follows established logging and error handling patterns

### Testing

Dev Note: Story Requires the following tests:

- [x] Vitest Unit Tests: (nextToFile: true), coverage requirement: 80%
- [x] Integration Test: location: `apps/trading-app/tests/agents/execution-agent.integration.test.ts`
- [ ] E2E: location: `/e2e/agents/trade-execution-lifecycle.test.ts`

Manual Test Steps:
- Start ExecutionAgent and verify Redis signal subscription
- Test pre-flight checks with various global/market status scenarios
- Simulate ENTRY_SIGNAL and verify market order placement with trailing stop
- Test trade state management and ExhaustionDetectionAgent coordination
- Simulate EXIT_SIGNAL and verify position closure and database recording
- Test startup reconciliation with existing Binance positions
- Verify orphan position handling and critical error logging
- Test complete trade lifecycle from entry signal to database record

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
