# Story 3.3: Risk Management Integration & Testing

## Status: Draft

## Story

- As a **System Operator**
- I want **comprehensive integration testing of risk management with all core trading agents**
- so that **I can be confident that protection mechanisms work correctly and the system maintains capital preservation as the top priority**

## Acceptance Criteria (ACs)

1. **AC 3.3.1 (GlobalRiskManager Integration):** All core agents **must** integrate correctly with GlobalRiskManager for position sizing and halt mechanisms
2. **AC 3.3.2 (Risk Limit Testing):** Risk limit breach scenarios **must** be tested and validated to halt trading appropriately
3. **AC 3.3.3 (Parameter Integration):** ThresholdManager parameter changes **must** propagate correctly to all agents without system restart
4. **AC 3.3.4 (Emergency Scenarios):** Emergency halt scenarios **must** be tested and handled gracefully with proper logging
5. **AC 3.3.5 (Performance Validation):** Risk management integration **must** not impact system performance beyond acceptable thresholds

## Tasks / Subtasks

- [ ] Task 1: GlobalRiskManager Integration Testing (AC: 3.3.1)
  - [ ] Create integration tests for GRM with ExecutionAgent position sizing
  - [ ] Test GRM concurrency control with multiple signal scenarios
  - [ ] Validate GRM halt mechanism integration with all trading agents
  - [ ] Test GRM P&L monitoring and drawdown calculation accuracy
  - [ ] Verify GRM startup reconciliation with existing positions

- [ ] Task 2: Risk Limit Breach Simulation & Testing (AC: 3.3.2)
  - [ ] Create test scenarios for daily drawdown limit breaches
  - [ ] Simulate maximum position limit violations
  - [ ] Test global trading halt propagation to all agents
  - [ ] Validate risk limit recovery and resume mechanisms
  - [ ] Test edge cases and boundary conditions for risk limits

- [ ] Task 3: Parameter Change Impact Testing (AC: 3.3.3)
  - [ ] Test ThresholdManager parameter updates during live operation
  - [ ] Validate parameter validation prevents invalid configurations
  - [ ] Test parameter rollback functionality
  - [ ] Verify parameter change auditing and logging
  - [ ] Test hot-reload capability without system restart

- [ ] Task 4: Emergency Scenario Validation (AC: 3.3.4)
  - [ ] Test manual emergency halt via Redis global status
  - [ ] Simulate critical system failures and recovery procedures
  - [ ] Validate orphaned trade handling and cleanup
  - [ ] Test system restart with existing risk states
  - [ ] Verify emergency logging and notification systems

- [ ] Task 5: Performance Impact Assessment (AC: 3.3.5)
  - [ ] Benchmark system performance with and without risk management
  - [ ] Test risk management under high-frequency trading scenarios
  - [ ] Validate sub-second signal processing requirements are maintained
  - [ ] Test memory and CPU impact of risk monitoring
  - [ ] Verify Redis performance under risk management load

- [ ] Task 6: End-to-End Integration Testing
  - [ ] Test complete trading workflows with risk management enabled
  - [ ] Validate all core workflow scenarios from architecture documentation
  - [ ] Test integration with monitoring and logging systems
  - [ ] Verify database consistency during risk management operations
  - [ ] Test system behavior under various market conditions

## Dev Technical Guidance

### Previous Story Insights
Stories 3.1 (GlobalRiskManager) and 3.2 (Enhanced ThresholdManager) are in Draft status. This integration story assumes both components are implemented and ready for testing.

### Core Workflow Scenarios to Test [Source: architecture/core-workflows.md]

**Scenario 1 - Successful Long Trade (Happy Path):**
- Test complete workflow: MarketRegimeAgent → OrderFlowImbalanceAgent → ExecutionAgent → ExhaustionDetectionAgent
- Validate GRM position sizing integration in ExecutionAgent
- Verify trade state management and database recording

**Scenario 2 - Trade Blocked by Global Risk Manager:**
- Test GRM P&L monitoring and drawdown detection
- Validate global trading halt via Redis `global:status:trading`
- Verify ExecutionAgent pre-flight safety checks block trades correctly

**Scenario 3 - Trade Exits via Trailing Stop-Loss:**
- Test alternative exit scenarios with risk management active
- Validate position reconciliation and state cleanup
- Verify database recording with proper exit reasons

**Scenario 4 - Human Operator Halts Trading:**
- Test manual halt mechanisms via parameter changes
- Validate immediate halt propagation to all agents
- Verify confirmation and logging systems

**Scenario 5 - Startup & State Reconciliation:**
- Test system startup with existing positions and risk states
- Validate GRM reconciliation with Binance positions
- Verify risk state recovery and consistency

### Integration Points to Test [Source: architecture/components.md]

**GlobalRiskManager Integration:**
- Position sizing calculation for ExecutionAgent
- Daily P&L monitoring and limit enforcement
- Global halt mechanism via Redis state management
- Concurrency control validation

**ThresholdManager Integration:**
- Parameter loading for all agents on startup
- Runtime parameter updates without restart
- Parameter validation and error handling
- Configuration change auditing

### Redis Integration Testing [Source: architecture/in-memory-data-store-redis.md]
**Critical Redis Keys to Test:**
- `global:status:trading` - Global halt mechanism
- `status:BTCUSDT:trade` - Trade concurrency control
- `config:MomentumV1:BTCUSDT` - Parameter management
- `signals:BTCUSDT` - Signal processing with risk checks

### File Locations [Source: architecture/source-tree.md]
- **Integration Tests:** `apps/trading-app/tests/integration/risk-management/`
- **E2E Tests:** `apps/trading-app/tests/e2e/risk-management/`
- **Test Utilities:** `apps/trading-app/tests/utils/risk-test-helpers.ts`
- **Mock Data:** `apps/trading-app/tests/fixtures/risk-scenarios.ts`

### Technical Constraints [Source: architecture/tech-stack.md]
- **Testing Framework:** Vitest 1.x for all test types
- **Performance Requirements:** Sub-second signal processing maintained
- **Database:** Test with Supabase PostgreSQL 15.x
- **Cache:** Test with Redis 7.2.x
- **Logging:** Validate Pino 9.x structured logging

### Testing Requirements
Dev Note: Story Requires the following tests:

- [ ] Vitest Unit Tests: (nextToFile: false), coverage requirement: 90% (higher for integration story)
- [ ] Vitest Integration Tests: location: `apps/trading-app/tests/integration/risk-management/`
- [ ] E2E Tests: location: `apps/trading-app/tests/e2e/risk-management/`

**Specific Test Suites Required:**
- `risk-manager-integration.test.ts` - GRM integration with all agents
- `parameter-management-integration.test.ts` - ThresholdManager integration
- `emergency-scenarios.test.ts` - Emergency halt and recovery testing
- `performance-impact.test.ts` - Performance validation tests
- `end-to-end-workflows.test.ts` - Complete workflow testing

Manual Test Steps:
- Dev will create comprehensive test scenarios covering all risk management features
- Simulate real trading conditions with risk limits and parameter changes
- Test system behavior under stress and failure conditions
- Validate all emergency procedures and recovery mechanisms
- Verify performance impact remains within acceptable bounds

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
