# Story 2.3: ExhaustionDetectionAgent Implementation

## Status: Draft

## Story

- As a **System Operator**
- I want **the system to monitor an open position for signs of momentum decay**
- so that **it can exit the trade adaptively when momentum exhaustion is detected**

## Acceptance Criteria (ACs)

1. **AC 3.1 (Activation):** The `ExhaustionDetectionAgent` **must** activate only when a trade is open
2. **AC 3.2 (Data Streaming):** When active, it **must** subscribe to `watchTrades()` and `watchOrderBook()` (depth: 50)
3. **AC 3.3 (Exit Conditions):** It **must** continuously monitor for "aggressor fatigue" (slowing delta) and "passive resistance" (liquidity walls)
4. **AC 3.4 (Signal Publishing):** The moment either condition is met, it **must** immediately publish an `EXIT_SIGNAL` to the Redis `signals:BTCUSDT` channel

## Tasks / Subtasks

- [ ] Task 1: Create ExhaustionDetectionAgent Class Structure (AC: 3.1)
  - [ ] Create apps/trading-app/src/agents/ExhaustionDetectionAgent.ts extending RealtimeAgent
  - [ ] Implement trade state monitoring using Redis `status:BTCUSDT:trade` key
  - [ ] Add activation/deactivation logic based on open trade status
  - [ ] Configure agent registration with LangGraph orchestration

- [ ] Task 2: Implement Dual WebSocket Stream Integration (AC: 3.2)
  - [ ] Integrate CCXT Pro for dual WebSocket connectivity
  - [ ] Implement `watchTrades('BTC/USDT:USDT')` stream subscription
  - [ ] Implement `watchOrderBook('BTC/USDT:USDT', 50)` stream subscription
  - [ ] Add robust WebSocket reconnection logic for both streams
  - [ ] Configure stream synchronization and data correlation

- [ ] Task 3: Build Aggressor Fatigue Detection (AC: 3.3)
  - [ ] Implement rolling delta calculation for momentum tracking
  - [ ] Create slowing delta detection algorithm (momentum decay)
  - [ ] Add configurable sensitivity parameters for fatigue detection
  - [ ] Implement trend analysis for delta velocity changes

- [ ] Task 4: Build Passive Resistance Detection (AC: 3.3)
  - [ ] Implement order book analysis for liquidity wall detection
  - [ ] Calculate liquidity concentration at key price levels
  - [ ] Add configurable liquidity wall percentage threshold
  - [ ] Create resistance level identification algorithms

- [ ] Task 5: Implement EXIT_SIGNAL Generation & Publishing (AC: 3.4)
  - [ ] Create EXIT_SIGNAL with reason ('AGGRESSOR_FATIGUE' | 'PASSIVE_RESISTANCE')
  - [ ] Implement immediate signal publishing to `signals:BTCUSDT` channel
  - [ ] Add signal validation and error handling
  - [ ] Create signal serialization utilities

- [ ] Task 6: Critical Failure Recovery & State Management
  - [ ] Implement critical failure detection and recovery mechanisms
  - [ ] Add state persistence for mid-trade recovery scenarios
  - [ ] Create startup reconciliation integration
  - [ ] Implement comprehensive logging and monitoring

## Dev Notes

### Technical Guidance

**Agent Type and Responsibility** [Source: architecture/components.md#ExhaustionDetectionAgent]:
- Component Type: Real-time, Stateful Stream Processor
- Responsibility: Monitors open trade to detect signs of momentum exhaustion for adaptive exit
- Core Logic: When trade is active, subscribes to both trade and order book streams, detects "aggressor fatigue" or "passive resistance", publishes EXIT_SIGNAL
- **CRITICAL:** If this agent dies mid-trade, system must recover via Startup Reconciliation workflow

**Binance WebSocket Integration** [Source: architecture/external-apis.md#Binance]:
- Use CCXT Pro for dual WebSocket streams: `watchTrades('BTC/USDT:USDT')` and `watchOrderBook('BTC/USDT:USDT', 50)`
- Real-Time Data Streams pattern (not REST API)
- Requires robust WebSocket reconnection management for both streams
- Must handle connection failures and stream interruptions gracefully

**Signal Data Structure** [Source: architecture/data-models.md#Signal]:
```typescript
{
  agent_name: 'ExhaustionDetectionAgent';
  signal_type: 'EXIT_SIGNAL';
  signal_data: {
    reason: 'AGGRESSOR_FATIGUE' | 'PASSIVE_RESISTANCE' | 'TRAILING_STOP';
  };
}
```

**Redis Integration Patterns** [Source: architecture/in-memory-data-store-redis.md]:
- Trade State Monitoring: Read `status:BTCUSDT:trade` (String) to detect open trades
- Signal Publishing: Publish to `signals:BTCUSDT` (Pub/Sub Channel)
- Producer: ExhaustionDetectionAgent publishes exit signals
- Consumer: ExecutionAgent subscribes to signals channel

**Strategy Parameters** [Source: architecture/data-models.md#MomentumV1Parameters]:
- order_book_depth: number (order book monitoring depth, default: 50)
- liquidity_wall_pct: number (liquidity resistance threshold)
- Additional exhaustion detection parameters loaded from ThresholdManager

**File Locations** [Source: architecture/source-tree.md]:
- Agent implementation: `apps/trading-app/src/agents/ExhaustionDetectionAgent.ts`
- Base class: `apps/trading-app/src/agents/base/RealtimeAgent.ts` (from Story 1.4)
- Binance service: `apps/trading-app/src/services/binance.ts`
- Signal utilities: `apps/trading-app/src/services/signals.ts`

**Technical Constraints**:
- Must extend RealtimeAgent base class from Story 1.4
- Stateful design to maintain momentum tracking state
- Only activates when trade is open (critical for performance)
- Dual stream processing with synchronization requirements
- Critical failure recovery is mandatory for trade safety

### Previous Story Context

Epic 1 (Stories 1.1-1.4) and Stories 2.1-2.2 provide the foundation:
- **Story 1.4:** RealtimeAgent base class and agent framework
- **Story 1.3:** Redis infrastructure for trade state monitoring and signal publishing
- **Story 2.2:** OrderFlowImbalanceAgent generates entry signals that create open trades

The ExhaustionDetectionAgent builds on this foundation by:
- Extending RealtimeAgent base class for dual stream processing lifecycle
- Monitoring trade state set by ExecutionAgent (Story 2.4)
- Using Redis Pub/Sub infrastructure for exit signal publishing
- Following established logging and error handling patterns

### Testing

Dev Note: Story Requires the following tests:

- [x] Vitest Unit Tests: (nextToFile: true), coverage requirement: 80%
- [x] Integration Test: location: `apps/trading-app/tests/agents/exhaustion-detection-agent.integration.test.ts`
- [ ] E2E: location: `/e2e/agents/momentum-exhaustion-detection.test.ts`

Manual Test Steps:
- Start ExhaustionDetectionAgent and verify it remains idle when no trade is open
- Test activation when Redis trade status indicates open position
- Verify dual WebSocket connections to Binance (trades + order book depth 50)
- Test aggressor fatigue detection with simulated slowing delta scenarios
- Test passive resistance detection with simulated liquidity wall scenarios
- Verify EXIT_SIGNAL publishing to Redis `signals:BTCUSDT` channel
- Test critical failure recovery and startup reconciliation integration
- Validate dual stream synchronization under high market activity

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
