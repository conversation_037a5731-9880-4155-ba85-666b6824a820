# Autonomous Trading System Architecture Document

This document outlines the overall project architecture for the Autonomous Trading System, including backend systems, shared services, and non-UI specific concerns. Its primary goal is to serve as the guiding architectural blueprint for AI-driven development, ensuring consistency and adherence to chosen patterns and technologies.

This system is designed as a containerized, event-driven application, decomposed into specialized agents orchestrated by LangGraph. A core principle is the separation of the high-frequency **'hot path'** for real-time state and messaging (managed by **Redis**) from the durable **'cold path'** for analytics and permanent record-keeping (managed by a **Supabase PostgreSQL** database). The entire system is designed for operational maturity, packaged with **Docker**, managed via **Docker Compose**, and deployed to a Virtual Private Server (VPS) through an automated CI/CD pipeline. Human oversight and dynamic control are facilitated through a Discord-based command interface, with a clear path to future self-optimization via a `Master AI Agent`.

**Relationship to Frontend Architecture:**
This project's primary interface is a Discord-based control plane. As such, a dedicated Frontend Architecture Document is not required. All UI/UX concerns are limited to the functionality of the Discord bot.

### **Starter Template or Existing Project**

N/A - This is a greenfield project. The architecture is designed from first principles. All tooling, containerization, and configuration will be set up manually as defined within this document.

### **Change Log**

| Date       | Version | Description      | Author   |
| :--------- | :------ | :--------------- | :------- |
| {{CUR_DATE}} | 1.0     | Initial Draft    | Winston  |

## Sections

- [Introduction](./introduction.md)
- [High Level Architecture](./high-level-architecture.md)
- [Tech Stack](./tech-stack.md)
- [Data Models](./data-models.md)
- [Components](./components.md)
- [External APIs](./external-apis.md)
- [Core Workflows](./core-workflows.md)
- [Database Schema](./database-schema.md)
- [Source Tree](./source-tree.md)
- [In-Memory Data Store (Redis)](./in-memory-data-store-redis.md)
