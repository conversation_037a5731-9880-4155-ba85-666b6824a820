# Story 4.4: Monitoring & Notification System

## Status: Draft

## Story

- As a **System Operator**
- I want **comprehensive real-time monitoring and notification capabilities through Discord**
- so that **I can stay informed about system performance, trade events, and critical alerts without constantly monitoring the system manually**

## Acceptance Criteria (ACs)

1. **AC 4.4.1 (Trade Notifications):** System **must** send real-time notifications for all trade open/close events with key details.
2. **AC 4.4.2 (System Alerts):** System **must** send notifications for errors, warnings, and status changes.
3. **AC 4.4.3 (Performance Summaries):** System **must** send periodic performance summary notifications.
4. **AC 4.4.4 (Risk Alerts):** System **must** send immediate notifications for risk management events.
5. **AC 4.4.5 (Configurable Preferences):** Operators **must** be able to configure notification preferences and filtering.

## Tasks / Subtasks

- [ ] Task 1: Implement MonitoringAgent Enhancement (AC: 4.4.1, 4.4.2)
  - [ ] Enhance existing MonitoringAgent for comprehensive Discord notifications
  - [ ] Implement trade event notification formatting and delivery
  - [ ] Add system alert notification with severity levels
  - [ ] Create notification queue management and delivery reliability
  - [ ] Add notification rate limiting and batching for high-frequency events

- [ ] Task 2: Trade Event Notification System (AC: 4.4.1)
  - [ ] Create trade open notification with entry details
  - [ ] Implement trade close notification with P&L summary
  - [ ] Add trade error notification with diagnostic information
  - [ ] Create position update notifications for significant changes
  - [ ] Implement trade milestone notifications (duration, P&L thresholds)

- [ ] Task 3: System Alert Notification Framework (AC: 4.4.2)
  - [ ] Implement error alert notifications with stack traces
  - [ ] Create warning notifications for system degradation
  - [ ] Add status change notifications (agent start/stop, connection issues)
  - [ ] Implement health check failure notifications
  - [ ] Create performance degradation alert notifications

- [ ] Task 4: Performance Summary Notifications (AC: 4.4.3)
  - [ ] Create daily performance summary notifications
  - [ ] Implement weekly/monthly performance reports
  - [ ] Add real-time P&L milestone notifications
  - [ ] Create win rate and trade statistics summaries
  - [ ] Implement performance trend analysis notifications

- [ ] Task 5: Risk Management Alert System (AC: 4.4.4)
  - [ ] Create immediate notifications for risk limit breaches
  - [ ] Implement drawdown alert notifications with severity levels
  - [ ] Add position size violation notifications
  - [ ] Create emergency halt notifications with reason codes
  - [ ] Implement risk recovery and resume notifications

- [ ] Task 6: Notification Preference Management (AC: 4.4.5)
  - [ ] Create notification preference configuration commands
  - [ ] Implement notification filtering by type and severity
  - [ ] Add notification scheduling and quiet hours
  - [ ] Create notification channel routing (different channels for different types)
  - [ ] Implement notification acknowledgment and management

## Dev Technical Guidance

### Previous Story Insights
Stories 4.1-4.3 establish the Discord bot foundation, configuration management, and strategy control. This story completes the monitoring ecosystem by implementing comprehensive notification capabilities.

### MonitoringAgent Architecture [Source: architecture/components.md]
**MonitoringAgent & LoggerWorker:**
- Component Type: Asynchronous Utility Workers
- Responsibility: MonitoringAgent sends human-readable notifications to Discord
- Integration: Real-time alerts and system status notifications
- Enhancement: Expand from basic notifications to comprehensive monitoring system

### Discord Integration Patterns [Source: architecture/external-apis.md]
**Discord API Integration:**
- Purpose: Primary interface for human monitoring and control
- Integration: Uses both simple Webhooks for notifications and full Bot User API
- Webhooks: For fire-and-forget notifications (trade events, alerts)
- Bot API: For interactive notifications with buttons and acknowledgments

### Redis Messaging Integration [Source: architecture/in-memory-data-store-redis.md]
**Critical Redis Patterns for Monitoring:**
- `signals:BTCUSDT` (Pub/Sub) - Real-time signals for entry/exit monitoring
- `queue:logging` (List) - High-speed buffer for log jobs and notifications
- `status:global:trading` (String) - Global system status monitoring
- `status:BTCUSDT:trade` (String) - Active trade status monitoring

### Notification Types and Formats

**Trade Event Notifications:**
```typescript
interface TradeNotification {
  type: 'TRADE_OPEN' | 'TRADE_CLOSE' | 'TRADE_ERROR';
  trade_id: string;
  market: string;
  direction: 'LONG' | 'SHORT';
  entry_price?: number;
  exit_price?: number;
  quantity: number;
  pnl?: number;
  pnl_percentage?: number;
  timestamp: Date;
  reason?: string;
}
```

**System Alert Notifications:**
```typescript
interface SystemAlert {
  type: 'ERROR' | 'WARNING' | 'INFO' | 'CRITICAL';
  component: string;
  message: string;
  details?: any;
  timestamp: Date;
  severity: 1 | 2 | 3 | 4 | 5;
}
```

**Performance Summary Notifications:**
```typescript
interface PerformanceSummary {
  period: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  total_trades: number;
  win_rate: number;
  total_pnl: number;
  max_drawdown: number;
  sharpe_ratio?: number;
  timestamp: Date;
}
```

### Database Integration [Source: architecture/database-schema.md]
**Notification Data Sources:**
- `trades` table for trade event notifications
- `logs` table for system alert notifications
- `performance_summaries` table for performance notifications
- Real-time Redis streams for immediate notifications

### Notification Delivery Patterns
**Webhook vs Bot API Usage:**
- Webhooks: Simple notifications (trade events, basic alerts)
- Bot API: Interactive notifications (confirmations, acknowledgments)
- Rate Limiting: Prevent Discord API rate limits with batching
- Retry Logic: Ensure critical notifications are delivered

### File Locations [Source: architecture/source-tree.md]
- **MonitoringAgent:** `apps/trading-app/src/agents/MonitoringAgent.ts` (enhance existing)
- **Notification Services:** `apps/trading-app/src/services/notifications/`
- **Webhook Handlers:** `apps/trading-app/src/services/discord/webhooks/`
- **Notification Types:** `packages/shared-types/src/notifications.ts`
- **Tests:** `apps/trading-app/tests/services/notifications/`

### Integration with Previous Stories
**Story 4.1 Integration:**
- Use established Discord bot authentication and security
- Leverage role-based access control for notification management
- Integrate with audit logging system

**Story 4.2 Integration:**
- Use configuration management for notification preferences
- Integrate with parameter change notifications
- Leverage confirmation dialog patterns

**Story 4.3 Integration:**
- Enhance system status monitoring with notifications
- Integrate with emergency command notifications
- Use established Redis monitoring patterns

### Technical Constraints [Source: architecture/tech-stack.md]
- **Language:** TypeScript 5.4.x with strict type safety
- **Discord Library:** Discord.js with webhook and bot API support
- **Database:** PostgreSQL 15.x via Supabase for notification history
- **Cache:** Redis 7.2.x for real-time notification queuing
- **Testing:** Vitest 1.x for unit and integration tests
- **Logging:** Pino 9.x for structured notification logging

### Testing Requirements
Dev Note: Story Requires the following tests:

- [ ] Vitest Unit Tests: (nextToFile: true), coverage requirement: 80%
- [ ] Vitest Integration Test: location: `apps/trading-app/tests/services/notifications.integration.test.ts`
- [ ] Manual Test Steps: Create test scenarios for all notification types

Manual Test Steps:
- Dev will test trade event notifications with simulated trades
- Test system alert notifications with various error conditions
- Verify performance summary notifications with test data
- Test risk management alert notifications with limit breaches
- Validate notification preference configuration and filtering
- Test notification delivery reliability and rate limiting

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
