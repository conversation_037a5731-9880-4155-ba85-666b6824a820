# Story 5.1: Backtesting Framework Foundation

## Status: Draft

## Story

- As a **System Operator**
- I want **a robust backtesting framework that can simulate historical trading using the exact same agent logic as the live system**
- so that **I can validate trading strategies against historical data with realistic simulation parameters before deploying them live**

## Acceptance Criteria (ACs)

1. **AC 8.1 (Code Re-use):** The backtester **must** use the exact same TypeScript agent logic as the live system.
2. **AC 8.2 (Data Input):** It **must** process historical trade data from local files (e.g., Parquet/CSV).
3. **AC 8.3 (Realistic Simulation):** It **must** model configurable trading fees and price slippage.
4. **AC 5.1.1 (Simulation Engine):** Framework **must** provide chronological data replay with time-based event scheduling.
5. **AC 5.1.2 (Mock Exchange):** Framework **must** provide a mock exchange interface that simulates order execution.

## Tasks / Subtasks

- [ ] Task 1: Historical Data Ingestion System (AC: 8.2)
  - [ ] Create data loader for CSV and Parquet file formats
  - [ ] Implement data validation and schema verification
  - [ ] Add support for multiple timeframes and data sources
  - [ ] Create data preprocessing and normalization utilities
  - [ ] Implement efficient data streaming for large datasets

- [ ] Task 2: Simulation Engine Core (AC: 5.1.1)
  - [ ] Create chronological event scheduler for backtesting
  - [ ] Implement time-based data replay mechanism
  - [ ] Add simulation clock and time management
  - [ ] Create event queue system for agent coordination
  - [ ] Implement simulation state management and checkpointing

- [ ] Task 3: Mock Exchange Interface (AC: 5.1.2, 8.3)
  - [ ] Create mock exchange that implements CCXT Pro interface
  - [ ] Implement realistic order execution simulation
  - [ ] Add configurable trading fees and slippage modeling
  - [ ] Create order book simulation with depth and liquidity
  - [ ] Implement market impact and execution delay modeling

- [ ] Task 4: Agent Integration Layer (AC: 8.1)
  - [ ] Create adapter layer to run live agents in simulation mode
  - [ ] Implement simulation-aware Redis mock for agent state
  - [ ] Add simulation database interface for trade recording
  - [ ] Create agent lifecycle management for backtesting
  - [ ] Implement agent communication and signal routing

- [ ] Task 5: Configuration and Parameter Management
  - [ ] Create backtesting configuration schema and validation
  - [ ] Implement strategy parameter loading from configuration files
  - [ ] Add simulation parameter management (fees, slippage, etc.)
  - [ ] Create environment variable management for backtesting
  - [ ] Implement configuration validation and error handling

- [ ] Task 6: Foundation Testing and Validation
  - [ ] Create comprehensive unit tests for all framework components
  - [ ] Implement integration tests for data ingestion and simulation
  - [ ] Add validation tests for agent integration accuracy
  - [ ] Create performance benchmarks for simulation speed
  - [ ] Implement accuracy validation against known scenarios

## Dev Technical Guidance

### Previous Story Insights
Epic 4 (Human Interface & Control Plane) stories establish the complete live trading system. This backtesting framework must reuse the exact same agent logic to ensure accuracy.

### Acceptance Criteria Context [Source: prd/feature-requirements.md#F8]
**F8: Backtesting Framework:**
- AC 8.1: Backtester must use exact same TypeScript agent logic as live system
- AC 8.2: Must process historical trade data from local files (Parquet/CSV)
- AC 8.3: Must model configurable trading fees and price slippage
- AC 8.4: Must be invokable via CLI script with strategy configuration file
- AC 8.5: Must generate summary report with key metrics (Sharpe, P&L, Max Drawdown)
- AC 8.6: Must produce detailed CSV log of all simulated trades

### Live Agent Architecture to Reuse [Source: architecture/core-workflows.md]
**Core Trading Workflow (Scenario 1):**
1. MarketRegimeAgent → Redis permission setting
2. OrderFlowImbalanceAgent → Real-time trade stream processing
3. ExecutionAgent → Order placement and risk management
4. ExhaustionDetectionAgent → Exit signal generation
5. GlobalRiskManager → Portfolio-level risk enforcement

**Critical Integration Points:**
- Redis state management for agent communication
- Signal publishing/subscribing via Redis Pub/Sub
- Database recording for trade persistence
- Exchange API calls for market data and order execution

### Data Models for Backtesting [Source: architecture/data-models.md]
**Trade Interface (Reuse Existing):**
```typescript
interface Trade {
  id: string; market_id: number; strategy_config_id: number;
  status: 'OPEN' | 'CLOSED' | 'ERROR'; direction: 'LONG' | 'SHORT';
  entry_timestamp: Date; exit_timestamp?: Date | null;
  entry_price: number; exit_price?: number | null;
  quantity: number; leverage: number; pnl?: number | null;
  pnl_percentage?: number | null; entry_signal_id?: string | null;
  exit_signal_id?: string | null; exit_reason?: string | null;
  created_at: Date; updated_at: Date;
}
```

**Historical Data Schema:**
```typescript
interface HistoricalTrade {
  timestamp: Date;
  symbol: string;
  price: number;
  quantity: number;
  side: 'buy' | 'sell';
}

interface HistoricalOrderBook {
  timestamp: Date;
  symbol: string;
  bids: [number, number][]; // [price, quantity]
  asks: [number, number][];
}
```

### Mock Exchange Implementation
**CCXT Pro Interface Compatibility:**
- Implement `watchTrades()` for OrderFlowImbalanceAgent
- Implement `watchOrderBook()` for market depth simulation
- Implement `createOrder()` for ExecutionAgent
- Implement `fetchBalance()` for account simulation
- Implement `fetchOHLCV()` for MarketRegimeAgent

**Realistic Simulation Parameters:**
- Trading fees: Configurable maker/taker fees (default: 0.02%/0.04%)
- Price slippage: Market impact based on order size
- Execution delay: Realistic latency simulation
- Order book depth: Liquidity modeling for large orders

### File Locations [Source: architecture/source-tree.md]
- **Backtesting Framework:** `apps/trading-app/src/backtesting/`
- **Data Loaders:** `apps/trading-app/src/backtesting/data/`
- **Simulation Engine:** `apps/trading-app/src/backtesting/simulation/`
- **Mock Exchange:** `apps/trading-app/src/backtesting/exchange/`
- **Agent Adapters:** `apps/trading-app/src/backtesting/adapters/`
- **Configuration:** `apps/trading-app/src/backtesting/config/`
- **Tests:** `apps/trading-app/tests/backtesting/`

### Redis Mock Implementation
**Simulation-Aware Redis Interface:**
- Mock Redis operations for agent state management
- Implement Pub/Sub simulation for signal routing
- Create isolated simulation state per backtest run
- Maintain compatibility with live Redis interface patterns

### Database Integration
**Simulation Database Interface:**
- Use same database schema as live system
- Implement transaction isolation for backtest runs
- Create temporary tables or namespacing for simulation data
- Ensure no interference with live trading data

### Technical Constraints [Source: architecture/tech-stack.md]
- **Language:** TypeScript 5.4.x with strict type safety
- **Runtime:** Node.js 20.x LTS
- **Data Processing:** Support for CSV and Parquet file formats
- **Testing:** Vitest 1.x for unit and integration tests
- **Logging:** Pino 9.x for structured logging
- **Package Management:** pnpm workspaces for monorepo structure

### Testing Requirements
Dev Note: Story Requires the following tests:

- [ ] Vitest Unit Tests: (nextToFile: true), coverage requirement: 85% (higher for framework)
- [ ] Vitest Integration Test: location: `apps/trading-app/tests/backtesting/framework.integration.test.ts`
- [ ] Manual Test Steps: Create test scenarios for historical data processing and agent simulation

Manual Test Steps:
- Dev will test historical data ingestion with sample CSV/Parquet files
- Test simulation engine with known historical scenarios
- Verify mock exchange produces realistic execution results
- Test agent integration maintains exact same logic as live system
- Validate configuration management and parameter loading
- Test simulation accuracy against expected outcomes

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
