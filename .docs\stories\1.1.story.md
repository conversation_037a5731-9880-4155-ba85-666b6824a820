# Story 1.1: Project Structure & Development Environment Setup

## Status: Draft

## Story

- As a **Developer**
- I want **a properly configured monorepo development environment with all necessary tooling**
- so that **I can efficiently develop the autonomous trading system with consistent code quality and deployment capabilities**

## Acceptance Criteria (ACs)

1. **AC 1.1 (Monorepo Structure):** The project **must** follow the exact directory structure defined in source-tree.md with pnpm workspaces
2. **AC 1.2 (TypeScript Configuration):** TypeScript 5.4.x **must** be configured with shared tsconfig for all packages
3. **AC 1.3 (Code Quality):** ESLint **must** be configured with shared rules across all packages
4. **AC 1.4 (Package Management):** pnpm 9.x **must** be configured with workspace dependencies working correctly
5. **AC 1.5 (Docker Setup):** Docker and Docker Compose **must** be configured for the trading-app container
6. **AC 1.6 (CI/CD Foundation):** Basic GitHub Actions workflow **must** be set up for automated testing and deployment
7. **AC 1.7 (Environment Configuration):** .env.example **must** be created with all required environment variables
8. **AC 1.8 (Build Pipeline):** The build process **must** compile TypeScript and prepare deployable artifacts

## Tasks / Subtasks

- [ ] Task 1: Create Root Monorepo Structure (AC: 1.1)
  - [ ] Initialize root package.json with pnpm workspace configuration
  - [ ] Create pnpm-workspace.yaml with apps/* and packages/* patterns
  - [ ] Create .gitignore with Node.js, TypeScript, and Docker exclusions
  - [ ] Set up root directory structure as per source-tree.md

- [ ] Task 2: Configure TypeScript & Code Quality (AC: 1.2, 1.3)
  - [ ] Create packages/config/tsconfig/ with base TypeScript configuration
  - [ ] Create packages/config/eslint/ with shared ESLint rules
  - [ ] Configure TypeScript 5.4.x with strict mode and modern target
  - [ ] Set up ESLint with TypeScript parser and recommended rules

- [ ] Task 3: Set Up Package Structure (AC: 1.1, 1.4)
  - [ ] Create apps/trading-app/ with src/ structure (agents/, components/, services/, workers/)
  - [ ] Create packages/shared-types/ for TypeScript interfaces
  - [ ] Create packages/utils/ for shared utility functions
  - [ ] Configure package.json files with proper dependencies and scripts

- [ ] Task 4: Docker Configuration (AC: 1.5)
  - [ ] Create Dockerfile for trading-app with Node.js 20.x LTS base
  - [ ] Create docker-compose.yml with trading-app, redis, and logger-worker services
  - [ ] Configure Docker networking and volume mounts
  - [ ] Set up environment variable handling in containers

- [ ] Task 5: CI/CD Pipeline Setup (AC: 1.6)
  - [ ] Create .github/workflows/deploy.yml for automated deployment
  - [ ] Configure GitHub Actions for TypeScript compilation and testing
  - [ ] Set up Docker image building and registry push
  - [ ] Configure deployment triggers and environment management

- [ ] Task 6: Environment & Configuration (AC: 1.7)
  - [ ] Create .env.example with all required environment variables
  - [ ] Document configuration requirements for Binance API, Discord, Supabase, Redis
  - [ ] Set up configuration loading in apps/trading-app/src/config.ts
  - [ ] Implement environment validation and error handling

## Dev Notes

### Technical Guidance

**Project Structure Requirements** [Source: architecture/source-tree.md]:
- Monorepo managed by pnpm workspaces
- Apps directory contains only trading-app (single deployable application)
- Packages directory contains config, shared-types, and utils
- Each package has proper separation of concerns and dependencies

**Technology Stack Requirements** [Source: architecture/tech-stack.md]:
- TypeScript 5.4.x for type safety and modern language features
- Node.js 20.x LTS for runtime execution
- pnpm 9.x for dependency management and monorepo orchestration
- Vitest 1.x for testing framework
- Docker Compose for containerization and deployment
- Pino 9.x for structured logging

**File Locations** [Source: architecture/source-tree.md]:
- Main application: `apps/trading-app/src/main.ts`
- Configuration: `apps/trading-app/src/config.ts`
- Shared types: `packages/shared-types/src/`
- Utilities: `packages/utils/src/`
- Docker config: `docker-compose.yml` (root level)
- CI/CD: `.github/workflows/deploy.yml`

**Technical Constraints**:
- Must use exact versions specified in tech-stack.md
- All packages must share TypeScript and ESLint configurations
- Docker setup must support multi-container deployment
- Environment variables must be properly validated and documented

### Testing

Dev Note: Story Requires the following tests:

- [x] Vitest Unit Tests: (nextToFile: true), coverage requirement: 80%
- [ ] Integration Test: location: `apps/trading-app/tests/setup.integration.test.ts`
- [ ] E2E: location: `/e2e/foundation/project-setup.test.ts`

Manual Test Steps:
- Run `pnpm install` to verify workspace dependencies resolve correctly
- Run `pnpm build` to verify TypeScript compilation works across all packages
- Run `docker-compose up` to verify container orchestration works
- Verify ESLint rules are enforced across all packages
- Test environment variable loading and validation

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
