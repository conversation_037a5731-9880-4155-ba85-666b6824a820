# Tech Stack

| Category | Technology | Version | Purpose | Rationale |
| :--- | :--- | :--- | :--- | :--- |
| **Language** | TypeScript | 5.4.x | Core application logic, agent development, type safety. | End-to-end type safety, modern language features, strong ecosystem. |
| **Runtime** | Node.js | 20.x (LTS) | Executes the compiled JavaScript code. | High-performance, non-blocking I/O, standard for TypeScript applications. |
| **Framework** | LangGraph | latest | Orchestration of AI agents and state management. | User-specified choice; LangGraph.js provides a native TS/JS interface. |
| **Database** | PostgreSQL | 15.x | Durable storage for trades, signals, analytics (Cold Path). | Robust, reliable SQL database provided by the Supabase platform. |
| **Cache / Broker** | Redis | 7.2.x | High-speed state, messaging, and job queue (Hot Path). | Industry standard for low-latency in-memory data operations. |
| **Testing** | Vitest | 1.x | Unit and integration testing for all TypeScript code. | Modern, fast, and simple test framework with native TS support. |
| **Dependency Mgmt**| pnpm | 9.x | Dependency management and monorepo orchestration. | Fast, disk-space efficient, and excellent support for monorepos. |
| **IaC Tool** | Docker Compose | latest | Defining and running the multi-container application on the VPS. | Simplifies management of services, networking, and volumes on a single host. |
| **Logging** | Pino | 9.x | High-performance, structured application logging. | Extremely fast, produces structured JSON logs by default. |
