# Data Models

This section defines the core data entities for the system, including PostgreSQL schema details and TypeScript interfaces.

## Market
- **Purpose:** Represents a financial instrument that the system is configured to trade.
- **TypeScript Interface:**
    ```typescript
    interface Market {
      id: number;
      symbol: string; // e.g., 'BTCUSDT'
      base_asset: string;
      quote_asset: string;
      is_active: boolean;
      created_at: Date;
    }
    ```

## StrategyConfig
- **Purpose:** Stores a versioned set of parameters for a specific trading strategy on a given market.
- **TypeScript Interface:**
    ```typescript
    interface MomentumV1Parameters {
      delta_threshold: number;
      trailing_stop_pct: number;
      imbalance_window_seconds: number;
      leverage: number;
      order_book_depth: number;
      liquidity_wall_pct: number;
    }

    interface StrategyConfig {
      id: number;
      market_id: number;
      strategy_name: string;
      version: number;
      parameters: MomentumV1Parameters;
      is_active: boolean;
      created_at: Date;
    }
    ```

## Signal
- **Purpose:** An immutable log of every significant decision or observation made by any agent.
- **TypeScript Interface:**
    ```typescript
    type Signal = { id: string; timestamp: Date; market_id: number; created_at: Date; } & (
      | { agent_name: 'MarketRegimeAgent'; signal_type: 'REGIME_PERMISSION'; signal_data: { permission: 'ALLOW_LONG' | 'ALLOW_SHORT' | 'BLOCK'; }; }
      | { agent_name: 'OrderFlowImbalanceAgent'; signal_type: 'ENTRY_SIGNAL'; signal_data: { direction: 'LONG' | 'SHORT'; price: number; delta: number; }; }
      | { agent_name: 'ExhaustionDetectionAgent'; signal_type: 'EXIT_SIGNAL'; signal_data: { reason: 'AGGRESSOR_FATIGUE' | 'PASSIVE_RESISTANCE' | 'TRAILING_STOP'; }; }
    );
    ```

## Trade
- **Purpose:** The master record for every executed trade, from open to close.
- **TypeScript Interface:**
    ```typescript
    interface Trade {
      id: string;
      market_id: number;
      strategy_config_id: number;
      status: 'OPEN' | 'CLOSED' | 'ERROR';
      direction: 'LONG' | 'SHORT';
      entry_timestamp: Date;
      exit_timestamp?: Date | null;
      entry_price: number;
      exit_price?: number | null;
      quantity: number;
      leverage: number;
      pnl?: number | null;
      pnl_percentage?: number | null;
      entry_signal_id?: string | null;
      exit_signal_id?: string | null;
      exit_reason?: string | null;
      created_at: Date;
      updated_at: Date;
    }
    ```

## PerformanceSummary
- **Purpose:** Stores a periodic, high-level summary of a strategy's performance, consumed by the `MasterAIAgent`.
- **TypeScript Interface:**
    ```typescript
    interface PerformanceSummary {
      id: string;
      strategy_config_id: number;
      period_start: Date;
      period_end: Date;
      total_trades: number;
      win_rate: number;
      pnl_percentage: number;
      sharpe_ratio: number;
      max_drawdown: number;
      parameters_used: MomentumV1Parameters;
      created_at: Date;
    }
    ```
