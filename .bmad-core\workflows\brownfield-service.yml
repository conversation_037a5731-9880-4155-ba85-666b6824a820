workflow:
  id: brownfield-service
  name: Brownfield Service/API Enhancement
  description: >-
    Agent workflow for enhancing existing backend services and APIs with new features,
    modernization, or performance improvements. Handles existing system analysis and safe integration.
  type: brownfield
  project_types:
    - service-modernization
    - api-enhancement
    - microservice-extraction
    - performance-optimization
    - integration-enhancement

  # For Complex Service Enhancements (Multiple Stories, Architectural Changes)
  complex_enhancement_sequence:
    - step: scope_assessment
      agent: any
      action: assess complexity
      notes: "First, assess if this is a simple service change (use simple_enhancement_sequence) or complex enhancement requiring full planning."

    - step: service_analysis
      agent: analyst
      action: analyze existing service
      notes: "Review existing service documentation, codebase, performance metrics, and identify integration dependencies."

    - agent: pm
      creates: brownfield-prd.md
      uses: brownfield-prd-tmpl
      requires: existing_service_analysis
      notes: "Creates comprehensive brownfield PRD focused on service enhancement with existing system analysis. SAVE OUTPUT: Copy final brownfield-prd.md to your project's docs/ folder."

    - agent: architect
      creates: brownfield-architecture.md
      uses: brownfield-architecture-tmpl
      requires: brownfield-prd.md
      notes: "Creates brownfield architecture with service integration strategy and API evolution planning. SAVE OUTPUT: Copy final brownfield-architecture.md to your project's docs/ folder."

    - agent: po
      validates: all_artifacts
      uses: po-master-checklist
      notes: "Validates all brownfield documents for service integration safety and API compatibility. May require updates to any document."

    - agent: various
      updates: any_flagged_documents
      condition: po_checklist_issues
      notes: "If PO finds issues, return to relevant agent to fix and re-export updated documents to docs/ folder."

    - workflow_end:
      action: move_to_ide
      notes: "All planning artifacts complete. Move to IDE environment to begin development. Explain to the user the IDE Development Workflow next steps: data#bmad-kb:IDE Development Workflow"

  # For Simple Service Enhancements (1-3 Stories, Following Existing Patterns)
  simple_enhancement_sequence:
    - step: enhancement_type
      action: choose approach
      notes: "Choose between creating single story (simple API endpoint) or epic (1-3 related service changes)."

    - agent: pm|po|sm
      creates: brownfield_epic OR brownfield_story
      uses: brownfield-create-epic OR brownfield-create-story
      notes: "Create focused service enhancement with existing API integration. Choose agent based on team preference and context."

    - workflow_end:
      action: move_to_ide
      notes: "Service enhancement defined. Move to IDE environment to begin development. Explain to the user the IDE Development Workflow next steps: data#bmad-kb:IDE Development Workflow"

  flow_diagram: |
    ```mermaid
    graph TD
        A[Start: Service Enhancement] --> B{Enhancement Complexity?}
        B -->|Complex/Significant| C[analyst: analyze existing service]
        B -->|Simple| D{1 Story or 2-3 Stories?}

        C --> E[pm: brownfield-prd.md]
        E --> F[architect: brownfield-architecture.md]
        F --> G[po: validate with po-master-checklist]
        G --> H{PO finds issues?}
        H -->|Yes| I[Return to relevant agent for fixes]
        H -->|No| J[Move to IDE Environment]
        I --> G

        D -->|1 Story| K[pm/po/sm: brownfield-create-story]
        D -->|2-3 Stories| L[pm/po/sm: brownfield-create-epic]
        K --> M[Move to IDE Environment]
        L --> M

        style J fill:#90EE90
        style M fill:#90EE90
        style E fill:#FFE4B5
        style F fill:#FFE4B5
        style K fill:#FFB6C1
        style L fill:#FFB6C1
    ```

  decision_guidance:
    use_complex_sequence_when:
      - Service enhancement requires multiple coordinated stories (4+)
      - API versioning or breaking changes needed
      - Database schema changes required
      - Performance or scalability improvements needed
      - Multiple integration points affected

    use_simple_sequence_when:
      - Adding simple endpoints or modifying existing ones
      - Enhancement follows existing service patterns
      - API compatibility maintained
      - Risk to existing service is low
      - Change is isolated with clear boundaries

  handoff_prompts:
    analyst_to_pm: "Service analysis complete. Create comprehensive brownfield PRD with service integration strategy."
    pm_to_architect: "Brownfield PRD ready. Save it as docs/brownfield-prd.md, then create the service architecture."
    architect_to_po: "Architecture complete. Save it as docs/brownfield-architecture.md. Please validate all artifacts for service integration safety."
    po_issues: "PO found issues with [document]. Please return to [agent] to fix and re-save the updated document."
    simple_to_ide: "Service enhancement defined with existing API integration. Move to IDE environment to begin development."
    complex_complete: "All brownfield planning artifacts validated and saved in docs/ folder. Move to IDE environment to begin development."
