# Feature Requirements

## Core Trading Engine

### **F1: Market Regime Analysis**
*   **User Story:** As the System Operator, I want the system to perform high-level market analysis so that it only attempts to trade when market conditions are suitable for the momentum strategy.
*   **Acceptance Criteria (AC):**
    *   **AC 1.1 (Scheduling):** The `MarketRegimeAgent` **must** run on a configurable schedule, defaulting to every **15 minutes**.
    *   **AC 1.2 (Data Fetching):** On each run, it **must** fetch the last 4 hours of OHLCV data for the `BTCUSDT` pair from the Binance API.
    *   **AC 1.3 (LLM Prompting):** It **must** construct a prompt for the Grok LLM to classify the current regime and to output a structured JSON response.
    *   **AC 1.4 (State Writing):** It **must** set the Redis key `permission:BTCUSDT:momentum` to `"ALLOW_LONG"`, `"ALLOW_SHORT"`, or `"BLOCK"`.
    *   **AC 1.5 (Fail-Safe):** The Redis key **must** be set with a Time-To-Live (TTL) of **20 minutes**.
    *   **AC 1.6 (Error Handling):** In the event of an LLM API failure or an unparsable response, the agent **must** log a critical error and default to setting the permission to `"BLOCK"`.

### **F2: Order Flow Imbalance Detection (Entry Signal)**
*   **User Story:** As the System Operator, I want the system to monitor real-time trade data and detect powerful, one-sided order flow so that it can generate a timely entry signal.
*   **Acceptance Criteria (AC):**
    *   **AC 2.1 (Permission Check):** The `OrderFlowImbalanceAgent` **must** remain idle if the Redis permission key is `"BLOCK"`.
    *   **AC 2.2 (Data Streaming):** It **must** maintain a persistent WebSocket connection to the Binance `watchTrades()` stream.
    *   **AC 2.3 (Delta Calculation):** It **must** calculate a rolling Taker Volume Delta over a configurable time window (default: 3 seconds).
    *   **AC 2.4 (Threshold Breach):** If the delta surpasses the configured threshold, it **must** immediately publish an `ENTRY_SIGNAL` message.
    *   **AC 2.5 (Signal Publishing):** The message **must** be published to the Redis Pub/Sub channel `signals:BTCUSDT`.

### **F3: Momentum Exhaustion Detection (Adaptive Exit)**
*   **User Story:** As the System Operator, I want the system to monitor an open position for signs of momentum decay so that it can exit the trade adaptively.
*   **Acceptance Criteria (AC):**
    *   **AC 3.1 (Activation):** The `ExhaustionDetectionAgent` **must** activate only when a trade is open.
    *   **AC 3.2 (Data Streaming):** When active, it **must** subscribe to `watchTrades()` and `watchOrderBook()` (depth: 50).
    *   **AC 3.3 (Exit Conditions):** It **must** continuously monitor for "aggressor fatigue" (slowing delta) and "passive resistance" (liquidity walls).
    *   **AC 3.4 (Signal Publishing):** The moment either condition is met, it **must** immediately publish an `EXIT_SIGNAL` to the Redis `signals:BTCUSDT` channel.

### **F4: Trade Execution and Management**
*   **User Story:** As the System Operator, I want a dedicated agent to reliably execute orders and manage the trade lifecycle.
*   **Acceptance Criteria (AC):**
    *   **AC 4.1 (Signal Subscription):** The `ExecutionAgent` **must** subscribe to the `signals:BTCUSDT` channel.
    *   **AC 4.2 (Pre-flight Checks):** It **must** verify global and market-specific trading status in Redis before placing an order.
    *   **AC 4.3 (Order Placement):** It **must** place a market order with the configured leverage (default: 10x).
    *   **AC 4.4 (Stop-Loss Placement):** Immediately after entry, it **must** place an exchange-side trailing stop-loss order (default: 0.15%).
    *   **AC 4.5 (Database Writing):** After a trade is closed, it **must** write a complete record to the `trades` table in Supabase.
    *   **AC 4.6 (State Reconciliation on Startup):** On application startup, it **must** query Binance for any open positions and re-hydrate its state to manage them.

## Risk Management & Control

### **F5: Global Risk Management**
*   **User Story:** As the System Operator, I want a global risk manager to enforce portfolio-level rules to protect my capital.
*   **Acceptance Criteria (AC):**
    *   **AC 5.1 (Position Sizing):** The GRM **must** calculate trade size based on a fixed-fractional model (e.g., risk 1% of equity).
    *   **AC 5.2 (Daily Drawdown Limit):** The GRM **must** periodically calculate the daily P&L. If it exceeds the configured limit (e.g., 3%), it **must** halt all new trading.
    *   **AC 5.3 (System Halt):** The halt is achieved by setting the `global:status:trading` key in Redis to a HALTED state with a 24-hour TTL.
    *   **AC 5.4 (Concurrency Control):** The system **must** not open more than the configured maximum number of concurrent trades (default: 1).

### **F6: Dynamic Parameter Control (`ThresholdManager`)**
*   **User Story:** As the System Operator, I want to view and update the system's strategy parameters in real-time without redeploying code.
*   **Acceptance Criteria (AC):**
    *   **AC 6.1 (Centralized Storage):** All tunable parameters **must** be stored in a single Redis `HASH`.
    *   **AC 6.2 (Configuration Loading):** On startup, all agents **must** load their parameters from this Redis hash.
    *   **AC 6.3 (Input Validation):** The system **must** reject attempts to set invalid parameter values (e.g., negative leverage).

### **F7: Human-in-the-Loop Interface (Discord Bot)**
*   **User Story:** As the System Operator, I want to use simple Discord commands to securely monitor and control the trading system.
*   **Acceptance Criteria (AC):**
    *   **AC 7.1 (View/Update Commands):** The bot **must** provide `/get_config` and `/set_config` slash commands.
    *   **AC 7.2 (Toggle Command):** The bot **must** provide a `/toggle_strategy [on|off]` command.
    *   **AC 7.3 (Security):** Modification commands **must** be restricted to users with a `SystemOperator` Discord role.
    *   **AC 7.4 (Confirmation Dialog):** All modification commands **must** present an "Are you sure?" confirmation dialog with buttons before execution.
    *   **AC 7.5 (Audit Logging):** All successful changes **must** be logged with the user, old value, and new value.

## Validation & Future Readiness

### **F8: Backtesting Framework**
*   **User Story:** As the System Operator, I want to run my trading strategy against historical data to validate its profitability and risk profile.
*   **Acceptance Criteria (AC):**
    *   **AC 8.1 (Code Re-use):** The backtester **must** use the exact same TypeScript agent logic as the live system.
    *   **AC 8.2 (Data Input):** It **must** process historical trade data from local files (e.g., Parquet/CSV).
    *   **AC 8.3 (Realistic Simulation):** It **must** model configurable trading fees and price slippage.
    *   **AC 8.4 (Parameterized Runs):** It **must** be invokable via a CLI script that accepts a strategy configuration file.
    *   **AC 8.5 (Performance Report Output):** It **must** generate a summary report with key metrics (Sharpe, P&L, Max Drawdown, etc.).
    *   **AC 8.6 (Trade Log Output):** It **must** produce a detailed CSV log of all simulated trades.

### **F9: Foundation for Self-Optimization (Master AI)**
*   **User Story:** As the System Operator, I want the system architected to support a future Master AI Agent for self-optimization.
*   **Acceptance Criteria (AC):**
    *   **AC 9.1 (Analytics Engine):** A scheduled service **must** be created to periodically calculate and save strategy KPIs to a `performance_summaries` table in Supabase.
    *   **AC 9.2 (Master AI Agent - "Advisor" Mode):** The initial `MasterAIAgent` **must**, after analyzing performance, send its suggested parameter change to a Discord channel for human approval.
    *   **AC 9.3 (Human-in-the-Loop Interaction):** The Discord suggestion message **must** include **[Apply Change]** and **[Ignore]** buttons. Clicking "Apply" executes the change via the `ThresholdManager`.
