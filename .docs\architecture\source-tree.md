# Source Tree

The project will be structured as a **monorepo managed by `pnpm` workspaces**.

## Directory Structure
```plaintext
autonomous-trading-system/
├── .github/
│   └── workflows/
│       └── deploy.yml
├── apps/
│   └── trading-app/
│       ├── src/
│       │   ├── agents/
│       │   ├── components/
│       │   ├── services/
│       │   ├── workers/
│       │   ├── main.ts
│       │   └── config.ts
│       ├── tests/
│       ├── Dockerfile
│       └── package.json
├── packages/
│   ├── config/
│   │   ├── eslint/
│   │   └── tsconfig/
│   ├── shared-types/
│   │   ├── src/
│   │   └── package.json
│   └── utils/
│       ├── src/
│       └── package.json
├── .env.example
├── docker-compose.yml
├── package.json
├── pnpm-workspace.yaml
└── tsconfig.json
```

## Package Responsibilities
*   **`apps/trading-app`**: The only deployable application, containing all agent logic and the main entry point.
*   **`packages/config`**: Contains shared ESLint and TypeScript configurations to enforce consistent code quality.
*   **`packages/shared-types`**: The "data contract" defining all TypeScript interfaces for database models, API responses, and Redis structures.
*   **`packages/utils`**: A shared "toolbox" for common utility functions to prevent code duplication.
