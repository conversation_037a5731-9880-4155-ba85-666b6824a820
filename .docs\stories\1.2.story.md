# Story 1.2: Database Schema & Supabase Integration

## Status: Draft

## Story

- As a **System Developer**
- I want **a complete PostgreSQL database schema implemented in Supabase with proper connection management**
- so that **the trading system can reliably store and retrieve trades, signals, and analytics data**

## Acceptance Criteria (ACs)

1. **AC 1.1 (Schema Creation):** All 6 database tables **must** be created exactly as specified in database-schema.md
2. **AC 1.2 (Indexes & Constraints):** All indexes, foreign keys, and check constraints **must** be implemented correctly
3. **AC 1.3 (Triggers & Functions):** The timestamp trigger function **must** be created and applied to the trades table
4. **AC 1.4 (Supabase Connection):** Database connection **must** be established using direct connection string with proper authentication
5. **AC 1.5 (Connection Pooling):** Connection pool **must** be implemented to manage database connections efficiently
6. **AC 1.6 (TypeScript Interfaces):** All data model TypeScript interfaces **must** be created in packages/shared-types
7. **AC 1.7 (Database Utilities):** Core database utility functions **must** be implemented for CRUD operations
8. **AC 1.8 (Security Configuration):** Supabase project **must** be configured with network restrictions for VPS access only

## Tasks / Subtasks

- [ ] Task 1: Create Database Schema in Supabase (AC: 1.1, 1.2, 1.3)
  - [ ] Execute DDL script to create all 6 tables: markets, strategy_configs, signals, trades, logs, performance_summaries
  - [ ] Create all indexes: idx_signals_timestamp, idx_signals_market_id_timestamp, idx_trades_status, idx_trades_market_id_entry_timestamp, idx_logs_timestamp, idx_perf_summary_strategy_period
  - [ ] Implement foreign key constraints and check constraints for data integrity
  - [ ] Create trigger_set_timestamp() function and apply to trades table

- [ ] Task 2: Implement TypeScript Data Models (AC: 1.6)
  - [ ] Create packages/shared-types/src/database.ts with all interfaces
  - [ ] Implement Market, StrategyConfig, Signal, Trade, PerformanceSummary interfaces
  - [ ] Create MomentumV1Parameters interface for strategy configuration
  - [ ] Add proper TypeScript types for JSONB fields and enums

- [ ] Task 3: Set Up Supabase Connection & Security (AC: 1.4, 1.8)
  - [ ] Configure Supabase project with PostgreSQL 15.x
  - [ ] Set up direct database connection string authentication
  - [ ] Configure network restrictions for VPS IP address only
  - [ ] Test connection from development environment

- [ ] Task 4: Implement Database Connection Management (AC: 1.5)
  - [ ] Create packages/utils/src/database.ts with connection pool implementation
  - [ ] Configure connection pool with appropriate size and timeout settings
  - [ ] Implement connection health checks and retry logic
  - [ ] Add proper error handling for connection failures

- [ ] Task 5: Create Database Utility Functions (AC: 1.7)
  - [ ] Implement CRUD operations for all tables
  - [ ] Create query builders for complex operations (trade history, performance summaries)
  - [ ] Add transaction support for multi-table operations
  - [ ] Implement proper error handling and logging for database operations

- [ ] Task 6: Database Integration Testing (AC: 1.1-1.8)
  - [ ] Test all table creation and constraints
  - [ ] Verify foreign key relationships work correctly
  - [ ] Test connection pooling under load
  - [ ] Validate TypeScript interfaces match database schema exactly

## Dev Notes

### Technical Guidance

**Database Schema Requirements** [Source: architecture/database-schema.md]:
- 6 core tables: markets, strategy_configs, signals, trades, logs, performance_summaries
- Complete DDL script with all constraints, indexes, and triggers provided
- UUID primary keys for trades, BIGSERIAL for signals and logs, SERIAL for others
- JSONB fields for flexible data storage (parameters, signal_data, metadata)
- Proper foreign key relationships with CASCADE and RESTRICT policies

**Data Model Interfaces** [Source: architecture/data-models.md]:
- Market: Basic trading instrument definition
- StrategyConfig: Versioned strategy parameters with JSONB storage
- Signal: Union type for different agent signal types with discriminated unions
- Trade: Complete trade lifecycle from entry to exit with P&L tracking
- PerformanceSummary: Aggregated KPIs for Master AI Agent consumption

**Supabase Integration Requirements** [Source: architecture/external-apis.md]:
- Direct database connection string authentication
- Connection pool implementation required for efficiency
- Network restrictions to VPS IP address only for security
- PostgreSQL 15.x version requirement

**File Locations** [Source: architecture/source-tree.md]:
- TypeScript interfaces: `packages/shared-types/src/database.ts`
- Database utilities: `packages/utils/src/database.ts`
- Connection configuration: `apps/trading-app/src/config.ts`

**Technical Constraints**:
- Must use exact schema from database-schema.md without modifications
- All TypeScript interfaces must match database schema precisely
- Connection pool must handle reconnections and failures gracefully
- Security configuration must restrict access to authorized VPS only

### Previous Story Context

Story 1.1 established the monorepo structure and development environment. The database integration builds on this foundation by:
- Using the shared-types package for TypeScript interfaces
- Leveraging the utils package for database utilities
- Following the established project structure and coding standards

### Testing

Dev Note: Story Requires the following tests:

- [x] Vitest Unit Tests: (nextToFile: true), coverage requirement: 80%
- [x] Integration Test: location: `apps/trading-app/tests/database.integration.test.ts`
- [ ] E2E: location: `/e2e/foundation/database-operations.test.ts`

Manual Test Steps:
- Connect to Supabase and verify all tables exist with correct schema
- Run `npm run test:db-connection` to verify connection pool works
- Insert test data into each table and verify constraints work
- Test foreign key relationships by attempting invalid references
- Verify TypeScript interfaces compile without errors
- Test connection pool behavior under simulated load

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
