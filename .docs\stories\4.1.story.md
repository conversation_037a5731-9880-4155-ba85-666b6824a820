# Story 4.1: Discord Bot Foundation & Authentication

## Status: Draft

## Story

- As a **System Operator**
- I want **a secure Discord bot with role-based access control**
- so that **I can safely monitor and control the trading system through Discord commands without unauthorized access**

## Acceptance Criteria (ACs)

1. **AC 7.3 (Security):** Modification commands **must** be restricted to users with a `SystemOperator` Discord role.
2. **AC 7.5 (Audit Logging):** All successful changes **must** be logged with the user, old value, and new value.
3. **AC 4.1.1 (Bot Setup):** Discord bot application **must** be properly configured with secure token management.
4. **AC 4.1.2 (Command Framework):** Basic slash command framework **must** be implemented with proper registration.
5. **AC 4.1.3 (Security Validation):** All modification commands **must** validate user permissions before execution.

## Tasks / Subtasks

- [ ] Task 1: Discord Bot Application Setup (AC: 4.1.1)
  - [ ] Create Discord application and bot user in Discord Developer Portal
  - [ ] Configure bot permissions and OAuth2 scopes
  - [ ] Implement secure token management using environment variables
  - [ ] Set up bot invite link with proper permissions
  - [ ] Configure bot presence and status indicators

- [ ] Task 2: Bot Framework Implementation (AC: 4.1.2)
  - [ ] Create DiscordBot component in `apps/trading-app/src/services/`
  - [ ] Implement Discord.js client setup and connection management
  - [ ] Create slash command registration system
  - [ ] Implement command handler framework with routing
  - [ ] Add bot lifecycle management (startup, shutdown, reconnection)

- [ ] Task 3: Role-Based Access Control (AC: 7.3, 4.1.3)
  - [ ] Implement SystemOperator role validation middleware
  - [ ] Create permission checking utilities
  - [ ] Add role-based command filtering
  - [ ] Implement unauthorized access error handling
  - [ ] Create role management helper functions

- [ ] Task 4: Security Validation Framework (AC: 4.1.3)
  - [ ] Create security validation middleware for all commands
  - [ ] Implement user authentication verification
  - [ ] Add command permission matrix
  - [ ] Create security error responses and logging
  - [ ] Implement rate limiting for command usage

- [ ] Task 5: Audit Logging System (AC: 7.5)
  - [ ] Create audit logging service for Discord interactions
  - [ ] Implement user action tracking with timestamps
  - [ ] Add structured logging for all bot interactions
  - [ ] Create audit log database integration
  - [ ] Implement log retention and cleanup policies

- [ ] Task 6: Integration with Trading System
  - [ ] Connect DiscordBot to Redis for system state access
  - [ ] Integrate with ThresholdManager for configuration access
  - [ ] Add database connection for audit logging
  - [ ] Create system health monitoring integration
  - [ ] Implement graceful error handling and recovery

## Dev Technical Guidance

### Previous Story Insights
Epic 3 (Risk Management & Control Systems) stories are in Draft status. The Discord bot will need to integrate with GlobalRiskManager and ThresholdManager once they are implemented.

### Discord Bot Architecture [Source: architecture/components.md]
**DiscordBot (Control Plane):**
- Component Type: Human-in-the-Loop Interface
- Responsibility: Provides a secure interface for the operator to monitor and control the live system via Discord slash commands

### External API Integration [Source: architecture/external-apis.md]
**Discord API:**
- Purpose: Serves as the system's primary interface for human monitoring and control
- Integration: Uses both simple Webhooks for notifications and the full Bot User API for interactive slash commands
- Security: Requires role-based access control to restrict modification commands to the `SystemOperator` role

### High-Level Architecture Integration [Source: architecture/high-level-architecture.md]
**Container Structure:**
- Discord Command API runs within the `trading-app` container
- Integrates with LangGraph Agents for system control
- Connects to Redis for state management and configuration
- Uses Supabase for audit logging and data persistence

### Acceptance Criteria Context [Source: prd/feature-requirements.md#F7]
**F7: Human-in-the-Loop Interface (Discord Bot):**
- AC 7.1: Bot must provide `/get_config` and `/set_config` slash commands
- AC 7.2: Bot must provide `/toggle_strategy [on|off]` command
- AC 7.3: Modification commands must be restricted to `SystemOperator` role
- AC 7.4: All modification commands must present confirmation dialogs
- AC 7.5: All successful changes must be logged with user, old value, and new value

### File Locations [Source: architecture/source-tree.md]
- **Main Implementation:** `apps/trading-app/src/services/DiscordBot.ts`
- **Command Handlers:** `apps/trading-app/src/services/discord/commands/`
- **Security Middleware:** `apps/trading-app/src/services/discord/middleware/`
- **Shared Types:** `packages/shared-types/src/discord.ts`
- **Tests:** `apps/trading-app/tests/services/discord-bot.test.ts`

### Security Requirements
**Role-Based Access Control:**
- SystemOperator role required for all modification commands
- Read-only commands available to broader user base
- Unauthorized access attempts must be logged and blocked
- Role validation must occur before command execution

**Token Management:**
- Discord bot token stored securely in environment variables
- No hardcoded credentials in source code
- Proper token rotation and management procedures
- Secure bot invite link generation

### Integration Points
**Redis Integration:**
- Access to `global:status:trading` for system status
- Integration with `config:MomentumV1:BTCUSDT` for parameter access
- Connection to logging queue for audit trails

**Database Integration:**
- Audit logging to Supabase logs table
- User action tracking and retention
- Integration with existing database connection pool

### Technical Constraints [Source: architecture/tech-stack.md]
- **Language:** TypeScript 5.4.x with strict type safety
- **Runtime:** Node.js 20.x LTS
- **Discord Library:** Discord.js (latest stable version)
- **Database:** PostgreSQL 15.x via Supabase for audit logs
- **Cache:** Redis 7.2.x for state and configuration access
- **Testing:** Vitest 1.x for unit and integration tests
- **Logging:** Pino 9.x for structured logging

### Testing Requirements
Dev Note: Story Requires the following tests:

- [ ] Vitest Unit Tests: (nextToFile: true), coverage requirement: 80%
- [ ] Vitest Integration Test: location: `apps/trading-app/tests/services/discord-bot.integration.test.ts`
- [ ] Manual Test Steps: Create test Discord server and validate bot functionality

Manual Test Steps:
- Dev will create a test Discord server with SystemOperator role
- Test bot invitation and permission setup
- Verify role-based access control with different user roles
- Test command registration and basic command responses
- Validate audit logging for all user interactions
- Test security validation and unauthorized access handling

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
