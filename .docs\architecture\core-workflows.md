# Core Workflows

This section illustrates the primary operational flows of the system using UML Sequence Diagrams.

## Scenario 1: Successful Long Trade (Happy Path)
```mermaid
sequenceDiagram
    participant MA as MarketRegimeAgent
    participant IA as OrderFlowImbalanceAgent
    participant EA as ExecutionAgent
    participant EDA as ExhaustionDetectionAgent
    participant GRM as GlobalRiskManager
    participant Redis
    participant Exchange
    participant Supabase

    MA->>Exchange: Fetch historical data
    MA->>Redis: SET market:permission = "ALLOW_LONG"

    loop Real-time Trade Stream
        IA->>Exchange: watchTrades()
        IA->>Redis: GET market:permission
        Redis-->>IA: "ALLOW_LONG"
    end
    Note right of IA: Detects bullish imbalance!
    IA->>Redis: PUBLISH signals (ENTRY_SIGNAL)

    EA->>Redis: Receives ENTRY_SIGNAL
    EA->>GRM: Request Position Size
    GRM-->>EA: Return calculated size
    EA->>Exchange: CREATE MARKET BUY Order
    EA->>Exchange: CREATE TRAILING STOP Order
    Exchange-->>EA: Order Confirmations
    EA->>Redis: SET trade:status = "OPEN"

    loop Real-time Monitoring
        EDA->>Redis: GET trade:status
        Redis-->>EDA: "OPEN"
        EDA->>Exchange: watchOrderBook()
    end
    Note right of EDA: Detects exhaustion!
    EDA->>Redis: PUBLISH signals (EXIT_SIGNAL)

    EA->>Redis: Receives EXIT_SIGNAL
    EA->>Exchange: CREATE MARKET SELL Order
    Exchange-->>EA: Close Confirmation
    EA->>Redis: DEL trade:status
    EA->>Supabase: INSERT final trade record into 'trades' table
```

## Scenario 2: Trade Blocked by Global Risk Manager (Safety Override)
```mermaid
sequenceDiagram
    participant GRM as GlobalRiskManager
    participant IA as OrderFlowImbalanceAgent
    participant EA as ExecutionAgent
    participant Redis
    participant Supabase

    Note over GRM,Supabase: GRM periodically checks P&L from Supabase.
    GRM->>Supabase: SELECT SUM(pnl) FROM trades...
    Note over GRM: Max Drawdown Breached!
    GRM->>Redis: SET global:status:trading = "HALTED_MAX_DRAWDOWN"

    Note over IA,Redis: Later, a valid entry signal is found.
    IA->>Redis: PUBLISH signals (ENTRY_SIGNAL)

    EA->>Redis: Receives ENTRY_SIGNAL
    Note over EA: Performing pre-flight safety checks...
    EA->>Redis: GET global:status:trading
    Redis-->>EA: "HALTED_MAX_DRAWDOWN"
    
    Note over EA: Trade is BLOCKED.
    EA->>Redis: LPUSH queue:logging (Log "Trade Blocked by GRM")
```

## Scenario 3: Trade Exits via Trailing Stop-Loss (Alternative Exit)
```mermaid
sequenceDiagram
    participant EA as ExecutionAgent
    participant Exchange
    participant Redis
    participant Supabase

    Note over EA,Exchange: A trade is open and its trailing stop is live on the exchange.
    Note over Exchange: Price moves against the position...
    Exchange->>Exchange: Trailing Stop-Loss is triggered and executed.
    
    Note over EA,Exchange: The ExecutionAgent receives the fill notification.
    Exchange-->>EA: WebSocket notification: STOP_ORDER_FILLED
    
    EA->>EA: Reconcile position state.
    EA->>Redis: DEL trade:status
    EA->>Supabase: INSERT final trade record with exit_reason = 'TRAILING_STOP'
```

## Scenario 4: Human Operator Halts Trading via Discord
```mermaid
sequenceDiagram
    participant Operator as You
    participant Discord
    participant Bot as DiscordBot Component
    participant Redis

    Operator->>Discord: /toggle_strategy asset:BTCUSDT state:off
    Discord->>Bot: Sends Interaction event

    Bot->>Bot: Validate command & user permissions
    Bot->>Redis: HSET config:MomentumV1:BTCUSDT is_active "false"
    Redis-->>Bot: OK

    Bot->>Discord: Send confirmation message: "Strategy for BTCUSDT has been DISABLED."
    Discord-->>Operator: Display confirmation
```

## Scenario 5: Startup & State Reconciliation (MUST-FIX)
```mermaid
sequenceDiagram
    participant App as Application
    participant EA as ExecutionAgent
    participant Exchange
    participant Supabase
    participant EDA as ExhaustionDetectionAgent

    App->>EA: Start Process
    EA->>Exchange: fetchOpenPositions()
    Exchange-->>EA: Returns list of open positions

    loop For each open position
        EA->>Supabase: SELECT * FROM trades WHERE exchange_trade_id = [pos_id] AND status = 'OPEN'
        Supabase-->>EA: Returns trade record or null

        alt Trade record exists in DB
            EA->>EA: Re-hydrate state for this trade
            EA->>EDA: Start monitoring this position
        else Trade record does NOT exist (Critical Error)
            EA->>Exchange: CREATE MARKET CLOSE Order for position
            EA->>Redis: LPUSH queue:logging (Log CRITICAL error: "Orphan position closed")
        end
    end

    Note over EA: Reconciliation complete.
    EA->>Redis: Start listening to signals channel
```

## Scenario 6: Automated Optimization Loop
```mermaid
sequenceDiagram
    participant Scheduler
    participant Analytics as AnalyticsEngine
    participant MasterAI as MasterAIAgent
    participant Supabase
    participant LLM
    participant Redis
    participant Discord

    Scheduler->>Analytics: Trigger Daily Run
    Analytics->>Supabase: Query raw trades from `trades` table
    Supabase-->>Analytics: Return trade data
    Analytics->>Supabase: INSERT summary into `performance_summaries`

    Scheduler->>MasterAI: Trigger Optimization Run
    MasterAI->>Supabase: Read latest from `performance_summaries`
    Supabase-->>MasterAI: Return KPIs and parameters
    MasterAI->>LLM: Send structured prompt for analysis
    LLM-->>MasterAI: Return JSON with parameter change

    MasterAI->>Redis: HSET config hash with new parameter value
    MasterAI->>Discord: Send notification of the automated change
```
