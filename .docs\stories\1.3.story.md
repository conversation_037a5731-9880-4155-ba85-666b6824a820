# Story 1.3: Redis Infrastructure & Configuration Management

## Status: Draft

## Story

- As a **System Developer**
- I want **Redis configured with all required data structures and a ThresholdManager for dynamic parameter control**
- so that **agents can communicate via high-speed messaging and the system can be tuned in real-time without redeployment**

## Acceptance Criteria (ACs)

1. **AC 1.1 (Redis Setup):** Redis 7.2.x **must** be configured and accessible via Docker Compose
2. **AC 1.2 (Data Structures):** All 6 Redis data structures **must** be implemented as specified in in-memory-data-store-redis.md
3. **AC 1.3 (Key Naming Convention):** All Redis keys **must** follow the `[context]:[asset_symbol]:[type]` naming pattern
4. **AC 1.4 (Pub/Sub Channels):** Redis Pub/Sub channels **must** be configured for real-time agent communication
5. **AC 1.5 (ThresholdManager Implementation):** ThresholdManager **must** store and retrieve strategy parameters using Redis HASH
6. **AC 1.6 (Parameter Validation):** ThresholdManager **must** validate all parameter values before storing
7. **AC 1.7 (Connection Management):** Redis connection pool **must** be implemented with proper error handling and reconnection
8. **AC 1.8 (TTL Management):** Permission keys **must** support TTL for fail-safe behavior

## Tasks / Subtasks

- [ ] Task 1: Configure Redis Infrastructure (AC: 1.1)
  - [ ] Add Redis 7.2.x service to docker-compose.yml
  - [ ] Configure Redis with appropriate memory settings and persistence
  - [ ] Set up Redis networking and port configuration
  - [ ] Configure Redis authentication and security settings

- [ ] Task 2: Implement Redis Data Structures (AC: 1.2, 1.3)
  - [ ] Create Redis utility functions for all 6 data structure types
  - [ ] Implement permission state management (String with TTL)
  - [ ] Implement trade and global lock management (String)
  - [ ] Set up Pub/Sub channel management for signals
  - [ ] Create logging queue management (List)
  - [ ] Implement configuration parameter storage (Hash)

- [ ] Task 3: Build ThresholdManager Core (AC: 1.5, 1.6)
  - [ ] Create packages/utils/src/threshold-manager.ts
  - [ ] Implement parameter storage using Redis HASH operations
  - [ ] Create parameter retrieval with type safety
  - [ ] Implement parameter validation for MomentumV1Parameters
  - [ ] Add support for multiple strategy configurations

- [ ] Task 4: Implement Redis Connection Management (AC: 1.7)
  - [ ] Create packages/utils/src/redis.ts with connection pool
  - [ ] Configure connection pool with retry logic and health checks
  - [ ] Implement proper error handling for Redis failures
  - [ ] Add connection monitoring and logging
  - [ ] Set up graceful shutdown handling

- [ ] Task 5: Add TTL and State Management (AC: 1.8)
  - [ ] Implement TTL support for permission keys (20-minute default)
  - [ ] Create state management utilities for trade locks
  - [ ] Add global state management for system-wide controls
  - [ ] Implement automatic cleanup for expired keys

- [ ] Task 6: Create Redis TypeScript Interfaces (AC: 1.2-1.8)
  - [ ] Add Redis data structure types to packages/shared-types
  - [ ] Create interfaces for all Redis key patterns
  - [ ] Define Pub/Sub message types for agent communication
  - [ ] Add ThresholdManager interface definitions

## Dev Notes

### Technical Guidance

**Redis Data Structure Requirements** [Source: architecture/in-memory-data-store-redis.md]:
- 6 core data structures: Permission (String+TTL), Trade Lock (String), Global Lock (String), Signals (Pub/Sub), Logging (List), Config (Hash)
- Key naming convention: `[context]:[asset_symbol]:[type]` (e.g., `permission:BTCUSDT:momentum`)
- TTL support required for permission keys as fail-safe mechanism
- Pub/Sub channels for real-time agent communication

**ThresholdManager Requirements** [Source: prd/feature-requirements.md#F6]:
- Centralized storage of all tunable parameters in Redis HASH
- Runtime parameter loading for all agents on startup
- Input validation to reject invalid parameter values
- Support for real-time parameter updates without redeployment

**Strategy Parameters** [Source: architecture/data-models.md#MomentumV1Parameters]:
- delta_threshold: number (order flow imbalance threshold)
- trailing_stop_pct: number (stop-loss percentage)
- imbalance_window_seconds: number (calculation window)
- leverage: number (trading leverage multiplier)
- order_book_depth: number (order book monitoring depth)
- liquidity_wall_pct: number (liquidity resistance threshold)

**File Locations** [Source: architecture/source-tree.md]:
- Redis utilities: `packages/utils/src/redis.ts`
- ThresholdManager: `packages/utils/src/threshold-manager.ts`
- Redis types: `packages/shared-types/src/redis.ts`
- Docker configuration: `docker-compose.yml` (add Redis service)

**Technical Constraints**:
- Redis 7.2.x version requirement for latest features
- Connection pooling required for high-frequency operations
- TTL fail-safe mechanism prevents stale permissions
- All parameter changes must be validated before storage

### Previous Story Context

Stories 1.1 and 1.2 established the monorepo structure and database layer. The Redis infrastructure builds on this foundation by:
- Using the shared-types package for Redis interface definitions
- Leveraging the utils package for Redis utilities and ThresholdManager
- Following established project structure and coding standards
- Integrating with the Docker Compose setup from Story 1.1

### Testing

Dev Note: Story Requires the following tests:

- [x] Vitest Unit Tests: (nextToFile: true), coverage requirement: 80%
- [x] Integration Test: location: `apps/trading-app/tests/redis.integration.test.ts`
- [ ] E2E: location: `/e2e/foundation/redis-operations.test.ts`

Manual Test Steps:
- Start Redis via `docker-compose up redis` and verify connectivity
- Test ThresholdManager parameter storage and retrieval
- Verify Pub/Sub channels work for agent communication
- Test TTL behavior for permission keys (should expire after 20 minutes)
- Validate parameter validation rejects invalid values
- Test Redis connection pool under simulated load
- Verify all 6 data structure types work correctly

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
