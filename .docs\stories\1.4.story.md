# Story 1.4: Core Agent Framework & LangGraph Setup

## Status: Draft

## Story

- As a **System Developer**
- I want **a robust agent framework using LangGraph with shared utilities for logging, error handling, and lifecycle management**
- so that **all trading agents can be built consistently with proper orchestration and operational reliability**

## Acceptance Criteria (ACs)

1. **AC 1.1 (LangGraph Setup):** LangGraph latest **must** be configured and operational for agent orchestration
2. **AC 1.2 (Base Agent Classes):** Abstract base agent classes **must** be created for all agent types (Scheduled, Real-time, Reactive)
3. **AC 1.3 (Agent Lifecycle):** Agent lifecycle management **must** support start, stop, restart, and health monitoring
4. **AC 1.4 (Shared Utilities):** Common utilities **must** be implemented for logging, error handling, and configuration loading
5. **AC 1.5 (Event-Driven Architecture):** Agent communication **must** use Redis Pub/Sub for decoupled messaging
6. **AC 1.6 (Configuration Integration):** All agents **must** load parameters from ThresholdManager on startup
7. **AC 1.7 (Error Recovery):** Agents **must** have robust error handling with automatic restart capabilities
8. **AC 1.8 (Logging Framework):** Structured logging **must** be implemented using Pino 9.x with Redis queue integration

## Tasks / Subtasks

- [ ] Task 1: Set Up LangGraph Framework (AC: 1.1)
  - [ ] Install and configure LangGraph.js latest version
  - [ ] Create LangGraph orchestration setup in apps/trading-app/src/main.ts
  - [ ] Configure agent graph topology and state management
  - [ ] Set up LangGraph runtime and execution environment

- [ ] Task 2: Create Base Agent Classes (AC: 1.2, 1.3)
  - [ ] Create apps/trading-app/src/agents/base/BaseAgent.ts abstract class
  - [ ] Implement ScheduledAgent base class for time-based agents
  - [ ] Implement RealtimeAgent base class for stream processing agents
  - [ ] Implement ReactiveAgent base class for event-driven agents
  - [ ] Add agent lifecycle methods (start, stop, restart, health check)

- [ ] Task 3: Implement Shared Utilities (AC: 1.4, 1.8)
  - [ ] Create packages/utils/src/logger.ts with Pino configuration
  - [ ] Implement structured logging with Redis queue integration
  - [ ] Create packages/utils/src/error-handler.ts for centralized error management
  - [ ] Add configuration loading utilities with ThresholdManager integration
  - [ ] Implement health monitoring and metrics collection utilities

- [ ] Task 4: Set Up Event-Driven Communication (AC: 1.5)
  - [ ] Create agent communication layer using Redis Pub/Sub
  - [ ] Implement message serialization and deserialization
  - [ ] Add message routing and subscription management
  - [ ] Create signal publishing utilities for agent interactions

- [ ] Task 5: Integrate Configuration Management (AC: 1.6)
  - [ ] Connect agents to ThresholdManager for parameter loading
  - [ ] Implement runtime configuration updates without restart
  - [ ] Add configuration validation and error handling
  - [ ] Create configuration change notification system

- [ ] Task 6: Implement Error Recovery & Monitoring (AC: 1.7)
  - [ ] Add automatic restart logic for failed agents
  - [ ] Implement circuit breaker patterns for external dependencies
  - [ ] Create health check endpoints and monitoring
  - [ ] Add graceful shutdown handling for all agents

## Dev Notes

### Technical Guidance

**LangGraph Framework Requirements** [Source: architecture/tech-stack.md]:
- LangGraph latest version for AI agent orchestration and state management
- Native TypeScript/JavaScript interface for seamless integration
- Agent graph topology for managing complex agent interactions
- State management for maintaining agent context and data flow

**Agent Architecture Patterns** [Source: architecture/high-level-architecture.md]:
- Agent-Based Architecture with specialized, single-responsibility agents
- Event-Driven Communication using Redis Pub/Sub for decoupled messaging
- Hot/Cold Path Segregation with Redis for real-time and Supabase for persistence
- Dynamic Configuration via ThresholdManager for runtime parameter updates

**Agent Component Types** [Source: architecture/components.md]:
- **Scheduled Agents:** MarketRegimeAgent, AnalyticsEngine, MasterAIAgent (time-based execution)
- **Real-time Agents:** OrderFlowImbalanceAgent, ExhaustionDetectionAgent (stream processing)
- **Reactive Agents:** ExecutionAgent, MonitoringAgent (event-driven responses)
- **Utility Workers:** LoggerWorker, GlobalRiskManager (background processing)

**File Locations** [Source: architecture/source-tree.md]:
- Main application: `apps/trading-app/src/main.ts`
- Base agent classes: `apps/trading-app/src/agents/base/`
- Agent implementations: `apps/trading-app/src/agents/`
- Shared utilities: `packages/utils/src/`
- Logging configuration: `packages/utils/src/logger.ts`

**Technical Constraints**:
- Must use Pino 9.x for high-performance structured logging
- All agents must integrate with Redis for state and messaging
- Configuration must be loaded from ThresholdManager on startup
- Error handling must support automatic recovery and restart
- Agent lifecycle must be managed by LangGraph orchestration

### Previous Story Context

Stories 1.1-1.3 established the complete infrastructure foundation:
- **Story 1.1:** Monorepo structure and development environment
- **Story 1.2:** Database schema and Supabase integration
- **Story 1.3:** Redis infrastructure and ThresholdManager

The agent framework builds on this foundation by:
- Using the established project structure for agent organization
- Leveraging Redis infrastructure for agent communication and state
- Integrating with ThresholdManager for dynamic configuration
- Following the logging and error handling patterns

### Testing

Dev Note: Story Requires the following tests:

- [x] Vitest Unit Tests: (nextToFile: true), coverage requirement: 80%
- [x] Integration Test: location: `apps/trading-app/tests/agent-framework.integration.test.ts`
- [ ] E2E: location: `/e2e/foundation/agent-orchestration.test.ts`

Manual Test Steps:
- Start the agent framework via `npm run start:agents` and verify LangGraph initialization
- Test base agent lifecycle (start, stop, restart, health check)
- Verify agent communication via Redis Pub/Sub channels
- Test configuration loading from ThresholdManager
- Simulate agent failures and verify automatic restart behavior
- Check structured logging output and Redis queue integration
- Validate graceful shutdown of all agents

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
