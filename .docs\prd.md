Of course. Here is the complete, final **Product Requirements Document (PRD)** for the **Autonomous Trading System**.

This document is the consolidated output of our entire session, incorporating the maximally expanded details for every section. It serves as the definitive "source of truth" for what we are building and why.

***

# PRD: Autonomous Trading System

| Status | **FINAL** |
| :--- | :--- |
| **Author** | <PERSON> (Product Manager) |
| **Last Updated** | {{CUR_DATE}} |

## 1. Introduction

### 1.1 Problem Statement

**User Persona:** Consider "Alex," a technically-skilled quantitative trader. <PERSON> has developed a promising trading thesis based on identifying momentum through order flow imbalances but consistently fails to capture its full potential due to the operational realities of manual execution.

<PERSON> faces three core, interconnected problems that this system aims to solve:
*   **1. Inconsistent Strategy Application:** Despite having a clear set of rules, <PERSON>'s execution is impacted by **emotional bias**. In a real-time environment, fear can cause premature exits from winning trades, while greed can lead to holding losing trades too long, deviating from the optimal strategy.
*   **2. Sub-optimal Execution Price (Slippage):** The window of opportunity to enter a momentum trade is often less than 5 seconds. By the time <PERSON> observes the imbalance, confirms it, and manually enters the order, the price has often moved, resulting in significant **slippage** that erodes the profitability of the strategy.
*   **3. Inability to Scale & Monitor:** The strategy requires constant, focused monitoring of multiple high-frequency data streams (live trades, order book depth). This is not only mentally exhausting but **operationally unscalable**, preventing Alex from being active 24/7 or applying the strategy to more than one market at a time.

### 1.2 Solution Overview

The Autonomous Trading System is a containerized, agent-based platform designed to serve as a **disciplined, high-performance proxy for the expert human trader**. It systematically addresses the identified problems by codifying Alex's strategy and executing it with machine-like precision.

*   **Enforcing Absolute Discipline:** Its agent-based logic is entirely rule-based and deterministic at the point of execution.
    -   *Implementation:* All entry, exit, and risk management rules are codified into specialized TypeScript agents. This completely removes emotional input and ensures the strategy is applied with perfect consistency.
*   **Optimizing for Millisecond-Speed:** The system is architected to react to market events at a speed unachievable by a human.
    -   *Implementation:* A "hot path" architecture using real-time WebSockets from Binance and in-memory processing with Redis allows the system to detect and act on an imbalance signal in milliseconds, minimizing slippage.
*   **Providing Total Observability:** The system is not a "black box." It provides a transparent, real-time view into its decision-making process.
    -   *Implementation:* Structured logs are sent to a persistent Supabase database for deep analysis, while critical, human-readable alerts are pushed to a dedicated Discord channel, building operator trust.
*   **Guaranteeing Operator Control:** The system operates autonomously but is always under the ultimate authority of the human operator.
    -   *Implementation:* A secure Discord bot serves as a command-line interface, allowing the operator to dynamically tune all key strategy parameters (`Threshold Manager`) or activate an emergency "kill switch" to halt all trading instantly.

### 1.3 Goals and Objectives

| Goal | Objective | Key Success Metric(s) | Justification |
| :--- | :--- | :--- | :--- |
| **1. Achieve Profitable Execution** | To successfully deploy and run the defined momentum strategy. | Achieve a positive P&L with a Sharpe Ratio > 1.0 over a 3-month live period. | A Sharpe Ratio > 1.0 is a standard industry benchmark indicating that the strategy generates a positive risk-adjusted return. |
| **2. Ensure Capital Preservation** | To implement robust, multi-layered risk management. | The `GlobalRiskManager` prevents daily drawdown > 3%. No single trade loss exceeds 1% of total equity. | These hard limits ensure that a bug or a "black swan" market event cannot lead to catastrophic capital loss. |
| **3. Deliver Operational Excellence** | To provide a stable, observable, and controllable platform. | >99.9% uptime. All critical decisions are logged with sub-second precision. A full parameter change via Discord is confirmed in < 30 seconds. | The system's value is predicated on its reliability and the operator's ability to trust and control it. |
| **4. Enable Strategic Validation** | To build a reliable framework for testing and optimizing the strategy. | The backtester's P&L is within a 5% margin of a live forward-test on the same data. | This confirms the backtester is a reliable predictor of real-world performance, making it a trustworthy tool for strategy optimization. |
| **5. Build for Future Readiness** | To create a modular platform that can be easily extended. | A new "Mean Reversion" agent can be added to the system without requiring changes to the core trading engine. | This proves the architecture is decoupled and scalable, reducing the cost of future enhancements. |

### 1.4 Target Audience & "Jobs to be Done" (JTBD)

The sole user is the **System Operator**. The following "Jobs to be Done" frame the core needs the system must fulfill:

*   **JTBD 1 (Control):** "When I see an unexpected market event, I want to **immediately pause all trading activity**, so I can prevent the system from taking losses in an environment it wasn't designed for."
*   **JTBD 2 (Trust):** "When the system executes a trade, I want to **understand the exact data and reasoning that led to that decision**, so I can build confidence in its logic and identify areas for improvement."
*   **JTBD 3 (Adaptability):** "When I believe market volatility has changed, I want to **adjust the strategy's core parameters without redeploying code**, so I can adapt the system to the new environment quickly."
*   **JTBD 4 (Validation):** "Before I change a live parameter, I want to **test its impact against historical data**, so I can make data-driven decisions about the strategy's evolution."

### 1.5 Scope: In and Out

#### In Scope (for V1.0)

*   **Core Trading Engine:**
    -   `MarketRegimeAgent` for LLM-based strategic analysis.
    -   `OrderFlowImbalanceAgent` for entry signal generation.
    -   `ExhaustionDetectionAgent` for adaptive exit signal generation.
    -   `ExecutionAgent` for placing orders with 10x leverage.
*   **Global Risk Management (`GlobalRiskManager`):**
    -   Per-trade position sizing based on a fixed-fractional model.
    -   Configurable daily max drawdown limit ("kill switch").
    -   Configurable concurrency control (max 1 open position).
*   **Dynamic Control (`ThresholdManager` & Discord Bot):**
    -   Remote viewing and updating of all key strategy parameters via Discord slash commands.
    -   Real-time notifications for trade open/close and system alerts.
*   **Backtesting Framework:**
    -   Ability to run the agent logic against historical trade data.
    -   Configurable models for exchange fees and price slippage.
    -   Generation of a performance report with all key metrics.

#### Out of Scope (for V1.0)

*   **The `Master AI Agent`:**
    -   *Justification:* The system must first prove its operational stability and the viability of the manual control plane before introducing a layer of autonomous self-optimization.
*   **A Web-Based GUI:**
    -   *Justification:* The target user is a single operator proficient with Discord, making a web GUI an unnecessary development cost for V1.
*   **Multi-Asset / Multi-Strategy Support:**
    -   *Justification:* V1 is focused on perfecting the execution of a single strategy on a single market (BTCUSDT).
*   **Automated Backtest Data Pipeline:**
    -   *Justification:* For V1, the sourcing and preparation of historical data for the backtester will be a manual, offline process.

---

## 2. Feature Requirements

### 2.1 Core Trading Engine

#### **F1: Market Regime Analysis**
*   **User Story:** As the System Operator, I want the system to perform high-level market analysis so that it only attempts to trade when market conditions are suitable for the momentum strategy.
*   **Acceptance Criteria (AC):**
    *   **AC 1.1 (Scheduling):** The `MarketRegimeAgent` **must** run on a configurable schedule, defaulting to every **15 minutes**.
    *   **AC 1.2 (Data Fetching):** On each run, it **must** fetch the last 4 hours of OHLCV data for the `BTCUSDT` pair from the Binance API.
    *   **AC 1.3 (LLM Prompting):** It **must** construct a prompt for the Grok LLM to classify the current regime and to output a structured JSON response.
    *   **AC 1.4 (State Writing):** It **must** set the Redis key `permission:BTCUSDT:momentum` to `"ALLOW_LONG"`, `"ALLOW_SHORT"`, or `"BLOCK"`.
    *   **AC 1.5 (Fail-Safe):** The Redis key **must** be set with a Time-To-Live (TTL) of **20 minutes**.
    *   **AC 1.6 (Error Handling):** In the event of an LLM API failure or an unparsable response, the agent **must** log a critical error and default to setting the permission to `"BLOCK"`.

#### **F2: Order Flow Imbalance Detection (Entry Signal)**
*   **User Story:** As the System Operator, I want the system to monitor real-time trade data and detect powerful, one-sided order flow so that it can generate a timely entry signal.
*   **Acceptance Criteria (AC):**
    *   **AC 2.1 (Permission Check):** The `OrderFlowImbalanceAgent` **must** remain idle if the Redis permission key is `"BLOCK"`.
    *   **AC 2.2 (Data Streaming):** It **must** maintain a persistent WebSocket connection to the Binance `watchTrades()` stream.
    *   **AC 2.3 (Delta Calculation):** It **must** calculate a rolling Taker Volume Delta over a configurable time window (default: 3 seconds).
    *   **AC 2.4 (Threshold Breach):** If the delta surpasses the configured threshold, it **must** immediately publish an `ENTRY_SIGNAL` message.
    *   **AC 2.5 (Signal Publishing):** The message **must** be published to the Redis Pub/Sub channel `signals:BTCUSDT`.

#### **F3: Momentum Exhaustion Detection (Adaptive Exit)**
*   **User Story:** As the System Operator, I want the system to monitor an open position for signs of momentum decay so that it can exit the trade adaptively.
*   **Acceptance Criteria (AC):**
    *   **AC 3.1 (Activation):** The `ExhaustionDetectionAgent` **must** activate only when a trade is open.
    *   **AC 3.2 (Data Streaming):** When active, it **must** subscribe to `watchTrades()` and `watchOrderBook()` (depth: 50).
    *   **AC 3.3 (Exit Conditions):** It **must** continuously monitor for "aggressor fatigue" (slowing delta) and "passive resistance" (liquidity walls).
    *   **AC 3.4 (Signal Publishing):** The moment either condition is met, it **must** immediately publish an `EXIT_SIGNAL` to the Redis `signals:BTCUSDT` channel.

#### **F4: Trade Execution and Management**
*   **User Story:** As the System Operator, I want a dedicated agent to reliably execute orders and manage the trade lifecycle.
*   **Acceptance Criteria (AC):**
    *   **AC 4.1 (Signal Subscription):** The `ExecutionAgent` **must** subscribe to the `signals:BTCUSDT` channel.
    *   **AC 4.2 (Pre-flight Checks):** It **must** verify global and market-specific trading status in Redis before placing an order.
    *   **AC 4.3 (Order Placement):** It **must** place a market order with the configured leverage (default: 10x).
    *   **AC 4.4 (Stop-Loss Placement):** Immediately after entry, it **must** place an exchange-side trailing stop-loss order (default: 0.15%).
    *   **AC 4.5 (Database Writing):** After a trade is closed, it **must** write a complete record to the `trades` table in Supabase.
    *   **AC 4.6 (State Reconciliation on Startup):** On application startup, it **must** query Binance for any open positions and re-hydrate its state to manage them.

### 2.2 Risk Management & Control

#### **F5: Global Risk Management**
*   **User Story:** As the System Operator, I want a global risk manager to enforce portfolio-level rules to protect my capital.
*   **Acceptance Criteria (AC):**
    *   **AC 5.1 (Position Sizing):** The GRM **must** calculate trade size based on a fixed-fractional model (e.g., risk 1% of equity).
    *   **AC 5.2 (Daily Drawdown Limit):** The GRM **must** periodically calculate the daily P&L. If it exceeds the configured limit (e.g., 3%), it **must** halt all new trading.
    *   **AC 5.3 (System Halt):** The halt is achieved by setting the `global:status:trading` key in Redis to a HALTED state with a 24-hour TTL.
    *   **AC 5.4 (Concurrency Control):** The system **must** not open more than the configured maximum number of concurrent trades (default: 1).

#### **F6: Dynamic Parameter Control (`ThresholdManager`)**
*   **User Story:** As the System Operator, I want to view and update the system's strategy parameters in real-time without redeploying code.
*   **Acceptance Criteria (AC):**
    *   **AC 6.1 (Centralized Storage):** All tunable parameters **must** be stored in a single Redis `HASH`.
    *   **AC 6.2 (Configuration Loading):** On startup, all agents **must** load their parameters from this Redis hash.
    *   **AC 6.3 (Input Validation):** The system **must** reject attempts to set invalid parameter values (e.g., negative leverage).

#### **F7: Human-in-the-Loop Interface (Discord Bot)**
*   **User Story:** As the System Operator, I want to use simple Discord commands to securely monitor and control the trading system.
*   **Acceptance Criteria (AC):**
    *   **AC 7.1 (View/Update Commands):** The bot **must** provide `/get_config` and `/set_config` slash commands.
    *   **AC 7.2 (Toggle Command):** The bot **must** provide a `/toggle_strategy [on|off]` command.
    *   **AC 7.3 (Security):** Modification commands **must** be restricted to users with a `SystemOperator` Discord role.
    *   **AC 7.4 (Confirmation Dialog):** All modification commands **must** present an "Are you sure?" confirmation dialog with buttons before execution.
    *   **AC 7.5 (Audit Logging):** All successful changes **must** be logged with the user, old value, and new value.

### 2.3 Validation & Future Readiness

#### **F8: Backtesting Framework**
*   **User Story:** As the System Operator, I want to run my trading strategy against historical data to validate its profitability and risk profile.
*   **Acceptance Criteria (AC):**
    *   **AC 8.1 (Code Re-use):** The backtester **must** use the exact same TypeScript agent logic as the live system.
    *   **AC 8.2 (Data Input):** It **must** process historical trade data from local files (e.g., Parquet/CSV).
    *   **AC 8.3 (Realistic Simulation):** It **must** model configurable trading fees and price slippage.
    *   **AC 8.4 (Parameterized Runs):** It **must** be invokable via a CLI script that accepts a strategy configuration file.
    *   **AC 8.5 (Performance Report Output):** It **must** generate a summary report with key metrics (Sharpe, P&L, Max Drawdown, etc.).
    *   **AC 8.6 (Trade Log Output):** It **must** produce a detailed CSV log of all simulated trades.

#### **F9: Foundation for Self-Optimization (Master AI)**
*   **User Story:** As the System Operator, I want the system architected to support a future Master AI Agent for self-optimization.
*   **Acceptance Criteria (AC):**
    *   **AC 9.1 (Analytics Engine):** A scheduled service **must** be created to periodically calculate and save strategy KPIs to a `performance_summaries` table in Supabase.
    *   **AC 9.2 (Master AI Agent - "Advisor" Mode):** The initial `MasterAIAgent` **must**, after analyzing performance, send its suggested parameter change to a Discord channel for human approval.
    *   **AC 9.3 (Human-in-the-Loop Interaction):** The Discord suggestion message **must** include **[Apply Change]** and **[Ignore]** buttons. Clicking "Apply" executes the change via the `ThresholdManager`.

***

This document provides a complete and unambiguous definition of the product we are building. It is ready to be handed off to the Product Owner to begin creating the development backlog.

