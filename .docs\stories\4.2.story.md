# Story 4.2: Configuration Management Commands

## Status: Draft

## Story

- As a **System Operator**
- I want **to view and update system parameters through Discord slash commands**
- so that **I can dynamically tune the trading system in real-time without redeploying code**

## Acceptance Criteria (ACs)

1. **AC 7.1 (View/Update Commands):** The bot **must** provide `/get_config` and `/set_config` slash commands.
2. **AC 7.4 (Confirmation Dialog):** All modification commands **must** present an "Are you sure?" confirmation dialog with buttons before execution.
3. **AC 7.5 (Audit Logging):** All successful changes **must** be logged with the user, old value, and new value.
4. **AC 4.2.1 (Parameter Integration):** Commands **must** integrate with ThresholdManager for parameter validation and updates.
5. **AC 4.2.2 (Real-time Updates):** Parameter changes **must** propagate to all agents without system restart.

## Tasks / Subtasks

- [ ] Task 1: Implement `/get_config` Command (AC: 7.1)
  - [ ] Create slash command definition and registration
  - [ ] Implement parameter retrieval from ThresholdManager
  - [ ] Create formatted parameter display with current values
  - [ ] Add parameter descriptions and valid ranges
  - [ ] Implement error handling for configuration access failures

- [ ] Task 2: Implement `/set_config` Command (AC: 7.1, 7.4)
  - [ ] Create slash command with parameter name and value options
  - [ ] Implement parameter validation using ThresholdManager rules
  - [ ] Create confirmation dialog with "Are you sure?" buttons
  - [ ] Add parameter change preview showing old vs new values
  - [ ] Implement command cancellation and timeout handling

- [ ] Task 3: Parameter Validation Integration (AC: 4.2.1)
  - [ ] Integrate with ThresholdManager parameter validation
  - [ ] Implement comprehensive validation error messages
  - [ ] Add cross-parameter validation checking
  - [ ] Create validation result formatting for Discord
  - [ ] Implement parameter constraint checking and reporting

- [ ] Task 4: Confirmation Dialog System (AC: 7.4)
  - [ ] Create interactive button components for confirmations
  - [ ] Implement "Confirm" and "Cancel" button handlers
  - [ ] Add timeout handling for confirmation dialogs
  - [ ] Create confirmation message formatting with change summary
  - [ ] Implement dialog state management and cleanup

- [ ] Task 5: Audit Logging Implementation (AC: 7.5)
  - [ ] Create parameter change audit logging
  - [ ] Log user, timestamp, old value, and new value
  - [ ] Integrate with existing audit logging system from Story 4.1
  - [ ] Add structured logging for parameter changes
  - [ ] Implement audit log retention and cleanup

- [ ] Task 6: Real-time Parameter Propagation (AC: 4.2.2)
  - [ ] Implement parameter update notifications to all agents
  - [ ] Create hot-reload mechanism for parameter changes
  - [ ] Add parameter change broadcasting via Redis
  - [ ] Implement agent parameter refresh without restart
  - [ ] Add parameter change confirmation and status reporting

## Dev Technical Guidance

### Previous Story Insights
Story 4.1 (Discord Bot Foundation & Authentication) is in Draft status. This story builds upon the bot framework and security infrastructure established in 4.1.

### Acceptance Criteria Context [Source: prd/feature-requirements.md#F7]
**AC 7.1 (View/Update Commands):** Bot must provide `/get_config` and `/set_config` slash commands
**AC 7.4 (Confirmation Dialog):** All modification commands must present "Are you sure?" confirmation dialog with buttons
**AC 7.5 (Audit Logging):** All successful changes must be logged with user, old value, and new value

### ThresholdManager Integration [Source: stories/3.2.story.md]
**Enhanced ThresholdManager Capabilities:**
- Centralized parameter storage in Redis HASH (`config:MomentumV1:BTCUSDT`)
- Comprehensive parameter validation for all MomentumV1Parameters
- Parameter change auditing and logging
- Hot-reload capability for runtime parameter updates
- Cross-parameter validation and dependency checking

**Parameter Validation Rules:**
- `delta_threshold`: Must be positive number, range (0.1 to 100)
- `trailing_stop_pct`: Must be positive, between 0.01% and 5%
- `imbalance_window_seconds`: Must be positive integer, range 1-300 seconds
- `leverage`: Must be positive, maximum 20x for risk management
- `order_book_depth`: Must be positive integer, range 1-100
- `liquidity_wall_pct`: Must be positive, between 0.1% and 10%

### Data Models [Source: architecture/data-models.md]
**MomentumV1Parameters Interface:**
```typescript
interface MomentumV1Parameters {
  delta_threshold: number;        // Order flow imbalance threshold
  trailing_stop_pct: number;     // Stop-loss percentage
  imbalance_window_seconds: number; // Calculation window
  leverage: number;               // Trading leverage multiplier
  order_book_depth: number;      // Order book monitoring depth
  liquidity_wall_pct: number;    // Liquidity resistance threshold
}
```

### Discord Command Implementation Patterns
**Slash Command Structure:**
```typescript
// /get_config command
{
  name: 'get_config',
  description: 'View current system parameters',
  options: [
    {
      name: 'parameter',
      description: 'Specific parameter to view (optional)',
      type: ApplicationCommandOptionType.String,
      required: false
    }
  ]
}

// /set_config command
{
  name: 'set_config',
  description: 'Update system parameter',
  options: [
    {
      name: 'parameter',
      description: 'Parameter name to update',
      type: ApplicationCommandOptionType.String,
      required: true
    },
    {
      name: 'value',
      description: 'New parameter value',
      type: ApplicationCommandOptionType.Number,
      required: true
    }
  ]
}
```

### File Locations [Source: architecture/source-tree.md]
- **Command Handlers:** `apps/trading-app/src/services/discord/commands/config.ts`
- **Confirmation Dialogs:** `apps/trading-app/src/services/discord/components/confirmations.ts`
- **Parameter Utilities:** `apps/trading-app/src/services/discord/utils/parameters.ts`
- **Integration Layer:** `apps/trading-app/src/services/discord/integrations/threshold-manager.ts`
- **Tests:** `apps/trading-app/tests/services/discord/config-commands.test.ts`

### Redis Integration Patterns [Source: architecture/in-memory-data-store-redis.md]
- **Parameter Storage:** `config:MomentumV1:BTCUSDT` (Hash)
- **Parameter Updates:** Atomic HASH operations for parameter changes
- **Change Notifications:** Pub/Sub for parameter change broadcasting
- **Audit Logging:** Integration with logging queue for audit trails

### Security Integration [Source: stories/4.1.story.md]
**Role-Based Access Control:**
- SystemOperator role required for `/set_config` command
- Read access for `/get_config` available to broader user base
- Security validation middleware from Story 4.1
- Unauthorized access prevention and logging

### Technical Constraints [Source: architecture/tech-stack.md]
- **Language:** TypeScript 5.4.x with strict type safety
- **Discord Library:** Discord.js with slash command support
- **Database:** PostgreSQL 15.x via Supabase for audit logs
- **Cache:** Redis 7.2.x for parameter storage and notifications
- **Testing:** Vitest 1.x for unit and integration tests
- **Logging:** Pino 9.x for structured logging

### Testing Requirements
Dev Note: Story Requires the following tests:

- [ ] Vitest Unit Tests: (nextToFile: true), coverage requirement: 80%
- [ ] Vitest Integration Test: location: `apps/trading-app/tests/services/discord/config-commands.integration.test.ts`
- [ ] Manual Test Steps: Create test scenarios for parameter viewing and updating

Manual Test Steps:
- Dev will test `/get_config` command with various parameter combinations
- Test `/set_config` command with valid and invalid parameter values
- Verify confirmation dialogs work correctly with button interactions
- Test parameter validation and error message formatting
- Validate audit logging captures all parameter changes correctly
- Test real-time parameter propagation to trading agents

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
